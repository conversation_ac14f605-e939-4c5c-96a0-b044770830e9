<?xml version="1.0"?>
<odoo>
    <record id="view_approved_journal_wizard_report" model="ir.ui.view">
        <field name="name">account.approved.journal.wizard.report.form</field>
        <field name="model">account.approved.journal.wizard</field>
        <field name="type">form</field>
        <field name="arch" type="xml">
            <form string="Export Approved Journal">
                <group>
                    <group>
                        <field name="start_date" 
                        required="1"
                        />
                    </group>
                    <group>
                        <field name="end_date" 
                        required="1"
                        />
                    </group>
                </group>
                <footer>
                    <button name="action_export_excel" string="Export to Excel" type="object"
                        class="btn-primary" />
                    <button string="Cancel" class="btn-default" special="cancel" />
                </footer>
            </form>
        </field>
    </record>

    <record id="act_open_report_approved_journal_wizard" model="ir.actions.act_window">
        <field name="name">Export Approved Journal</field>
        <field name="res_model">account.approved.journal.wizard</field>
        <field name="type">ir.actions.act_window</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>

</odoo>