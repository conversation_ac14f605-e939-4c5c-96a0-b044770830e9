from odoo import models, fields, api
from odoo.exceptions import UserError, ValidationError


class WehaApprovedJournalReportWizard(models.TransientModel):
    _name = 'account.approved.journal.wizard'
    _description = 'Wizard Approved Journal Report'


    start_date = fields.Date(string='Start Date', required=False)
    end_date = fields.Date(string='End Date', required=False)


    def action_export_excel(self):

        return {
            'type': 'ir.actions.act_url',
            'url': f"/approved_journal_report/excel?start_date={self.start_date}&end_date={self.end_date}",
            'target': 'self',
            'close_on_report_download': True
        }
    