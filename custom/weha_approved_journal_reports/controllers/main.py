from odoo import http
from odoo.http import request, content_disposition
import io
import xlsxwriter
from datetime import datetime
import html


import logging
_logger = logging.getLogger(__name__)



class WehaApprovedJournalReport(http.Controller):

    @http.route('/approved_journal_report/excel', type='http', auth='user', csrf=False)
    def generate_approved_journal_report_excel(self, start_date=None, end_date=None):
        
        start_date_obj = datetime.strptime(str(start_date), '%Y-%m-%d').date()
        end_date_obj = datetime.strptime(str(end_date), '%Y-%m-%d').date()

        query = """
            SELECT a.date, c.name AS journal_name, b.debit, b.credit, d.name AS account_desc, 
            e.department_code AS unit, e.name AS unit_desc, a.rkap_code, b.description AS journal_description,
            a.name AS invoice_number, f.name AS po_number
            FROM account_move a
            LEFT JOIN account_move_line b ON a.id = b.move_id
            LEFT JOIN account_journal c ON a.journal_id = c.id
            LEFT JOIN account_account d ON b.account_id = d.id
            LEFT JOIN hr_department e ON a.unit_id = e.id
            LEFT JOIN purchase_order f ON a.purchase_order_id = f.id
            WHERE a.state='posted' AND a.date BETWEEN %s AND %s
        """
        request.env.cr.execute(query, (start_date_obj, end_date_obj))
        result = request.env.cr.dictfetchall()
        # Convert dates

        # Prepare Excel file
        output = io.BytesIO()
        workbook = xlsxwriter.Workbook(output, {'in_memory': True})
        sheet = workbook.add_worksheet("List Approved Journal")

        f1=workbook.add_format({'bold':True, 'border':2, 'border_color':'black'})
        f2=workbook.add_format({'border':1, 'border_color':'black'})
        f22=workbook.add_format({'border':1, 'border_color':'black', 'num_format': '#,##0.00'})
        f3=workbook.add_format({'bold':True})
        f33=workbook.add_format({'bold':True, 'num_format': '#,##0.00'})
        f4=workbook.add_format({'bold':True, 'font_size':14})
        
        # total = sum(stock_picking_id.move_ids_without_package.mapped('ammount_currency'))
        
        sheet.set_column('A:P', 20)
        sheet.write(1, 0, "Business Unit : ", f3)
        sheet.write(1, 1, str(self.env.user.company_id.name), f3)
        sheet.write(2, 0, "Supplier : ", f3)
        sheet.write(2, 1, '', f3)
        sheet.write(3, 0, "Date From : ", f3)
        sheet.write(3, 1, str(start_date), f3)
        sheet.write(4, 0, "Date To : ", f3)
        sheet.write(4, 1, str(end_date), f3)
        sheet.write(5, 0, "Printed By : ", f3)
        sheet.write(5, 1, str(self.env.user.name), f3)
        sheet.write(6, 0, "Printed Date : ", f3)
        sheet.write(6, 1, str(datetime.now().date()), f3)


        # Header
        headers = ['Accounting Date', 'Journal Name', 'Debit', 'Credit', 'Nature', 'Account Description', 'Unit', 'Unit Account Description', 
            'RKAP', 'RKAP Account Description', 'Journal Description', 'Invoice Number', 'PO NUMBER', 'FPJP NO', 'Vendor Name', 'Status Invoice']
        for col, header in enumerate(headers):
            sheet.write(8, col, header, f1)

        row = 9
        for rec in result:
            sheet.write(row, 0, str(rec['date']), f2)
            sheet.write(row, 1, str(rec['journal_name']['en_US']), f2)
            sheet.write(row, 2, rec['debit'], f22)
            sheet.write(row, 3, rec['credit'], f22)
            sheet.write(row, 4, '', f2)
            sheet.write(row, 5, str(rec['account_desc']['en_US']), f2)
            sheet.write(row, 6, str(rec['unit']), f2)
            sheet.write(row, 7, str(rec['unit_desc']), f2)
            sheet.write(row, 8, str(rec['rkap_code']), f2)
            sheet.write(row, 9, '', f2)
            sheet.write(row, 10, str(rec['journal_description']), f2)
            sheet.write(row, 11, str(rec['invoice_number']), f2)
            sheet.write(row, 12, str(rec['po_number']), f2)
            sheet.write(row, 13, '', f2)
            sheet.write(row, 14, '', f2)
            sheet.write(row, 15, '', f2)

            row += 1

        workbook.close()
        output.seek(0)

        filename = f"Approved_Journal_Report.xlsx"


        return request.make_response(
            output.read(),
            headers=[
                ('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'),
                ('Content-Disposition', content_disposition(filename))
            ]
        )
