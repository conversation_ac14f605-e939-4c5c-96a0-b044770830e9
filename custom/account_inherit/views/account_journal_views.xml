<odoo>
  <data>

    <record id="inherit_view_account_journal_form_id_inherit_module_account" model="ir.ui.view">
      <field name="name">account.journal.view.form.inherit</field>
      <field name="model">account.journal</field>
      <field name="inherit_id" ref="account.view_account_journal_form"/>
      <field name="arch" type="xml">
        <xpath expr="//field[@name='type']" position="after">
          <field name="is_netting" />
          <field name="is_time_deposit" />
          <!-- <field name="is_placement" />
          <field name="is_withdraw" /> -->
        </xpath>
        <xpath expr="//sheet/group/group[1]" position="after">
          <group >
            <field name="is_deposit_core" invisible='1' />
            <field name="is_deposit_sattlement" invisible='1' />
            <field name="is_apply" />
          </group>
        </xpath>
        <xpath expr="//page[@name='advanced_settings']" position="after">
          <page name="journal_time_deposit" string="Time Deposit" invisible="is_time_deposit != True">
            <group name="placement_account" string="Placement Account">
              <field name="journal_placement_id" required="is_time_deposit == True" />
              <field name="time_deposit_account_id" required="is_time_deposit == True" />
            </group>
            <group name="withdraw_account" string="WithDraw Account">
              <field name="journal_withdraw_id" required="is_time_deposit == True" />
              <field name="withdraw_account_id" required="is_time_deposit == True" />
              <field name="interest_account_id" required="is_time_deposit == True" />
              <field name="bank_charges_account_id" required="is_time_deposit == True" />
            </group>
          </page>
        </xpath>
        <xpath expr="//group[@name='bank_account_number']" position="before">
          <group name="deposit_settlement" invisible="is_deposit_sattlement == False and is_deposit_core == False">
            <field name="floating_account_id" />
            <field name="liabilities_account_id" />
          </group>
        </xpath>
        <xpath expr="//group[@name='bank_account_number']/field[@name='swift_code']" position="after">
          <field name="deposit_type" invisible="is_time_deposit != True" required="True"/>
        </xpath>
      </field>
    </record>

  </data>
</odoo>
