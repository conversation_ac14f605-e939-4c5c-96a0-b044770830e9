<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <data>
    
        <record id="account_netting.act_account_move_make_netting" model="ir.actions.act_window">
            <field name="name">Compensate</field>
            <field name="groups_id" eval="[Command.link(ref('account.group_account_manager'))]"/>
            <field name="context">{'default_move_line_ids': [(6, 0, active_ids)]}</field>

            <!-- <field name="groups_id" eval="[(4, ref('account.group_account_manager'))]" /> -->
        </record>

        <!-- Inherit form view untuk membatasi akses button Compensate -->
        <record id="inherit_view_account_move_make_netting_form" model="ir.ui.view">
            <field name="name">Compensate entries inherit</field>
            <field name="model">account.move.make.netting</field>
            <field name="inherit_id" ref="account_netting.view_account_move_make_netting_form"/>
            <field name="arch" type="xml">
                <xpath expr="//footer/button[@name='button_compensate']" position="attributes">
                    <attribute name="groups">account.group_account_manager</attribute>
                </xpath>
                <xpath expr="//field[@name='journal_id']" position="after">
                    <field name="move_line_ids" widget="many2many_tags" readonly="1" />
                    <field name="nominal" />
                    <field name="account_date" />
					<field name="period_id" invisible="0"/>
                </xpath>
            </field>
        </record>
    
    </data>
    

</odoo>
