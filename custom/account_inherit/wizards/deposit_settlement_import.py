from odoo import fields, api, models,_
import io
import xlsxwriter
import base64
from odoo.exceptions import ValidationError
from datetime import datetime, timedelta
from markupsafe import escape, Markup

import csv
from openpyxl import load_workbook
from io import BytesIO
import pandas as pd
import logging
import json
import re
_logger = logging.getLogger(__name__)


class DepositSettlementImport(models.TransientModel):
    _name = 'deposit.settlement.import'

    type_of_journal = fields.Selection([
        ('deposit', 'Deposit'),
        ('sattlement', 'Settlement')
    ], string='Type Of Journal',default="deposit", required=True)
    transaction_document = fields.Binary('Transaction Document', required=True)
    transaction_document_name = fields.Char('Transaction Document Name')
    created_user_id = fields.Many2one('res.users', string='Created By', default=lambda self: self.env.user, required=True, readonly=True)
    created_date = fields.Date('Created Date', default=datetime.today(), required=True, readonly=True)
    deposit_history_id = fields.Many2one('deposit.settlement.history', string='Deposit History')

    def generate_journal_entries(self):
        for rec in self:
            rec._validate_file_and_determine_type()
            rec.import_data_from_excel()
    
    def _validate_file_and_determine_type(self):
        """Validasi file extension dan menentukan type_of_journal berdasarkan kolom TRX_SUB_TYPE"""
        if not self.transaction_document:
            raise ValidationError("Please upload a file.")

        file_name = self.transaction_document_name.lower()
        file_content = base64.b64decode(self.transaction_document)
        
        # Validasi file extension
        if not file_name.endswith(('.csv', '.xls', '.xlsx')):
            raise ValidationError("Unsupported file type. Use CSV or Excel files only (.csv, .xls, .xlsx).")
        
        
        try:
            if file_name.endswith('.csv'):
                df = pd.read_csv(io.BytesIO(file_content), skipinitialspace=True)
            elif file_name.endswith(('.xls', '.xlsx')):
                df = pd.read_excel(io.BytesIO(file_content))
                
            df.columns = df.columns.str.replace(' ', '', regex=False)
            
            
            if 'TRX_SUB_TYPE' not in df.columns and 'TRXSUBTYPE' not in df.columns:
                raise ValidationError("Column 'TRX_SUB_TYPE' not found in the file. Please make sure the file contains this column.")
            
            
            trx_sub_type_col = 'TRX_SUB_TYPE' if 'TRX_SUB_TYPE' in df.columns else 'TRXSUBTYPE'
            
            
            if len(df) == 0:
                raise ValidationError("The file is empty. Please ensure the file contains data.")
                
            first_row_value = df[trx_sub_type_col].iloc[0]
            
            
            if pd.isna(first_row_value) or str(first_row_value).strip() == '':
                raise ValidationError("The first row of column 'TRX_SUB_TYPE' is empty. Please ensure the file contains valid transaction sub type.")
            
            
            trx_type = str(first_row_value).strip().upper()
            
            
            valid_types = {'DEPOSIT', 'SETTLEMENT'}
            if trx_type not in valid_types:
                raise ValidationError(f"Invalid TRX_SUB_TYPE value '{trx_type}' found in first row. Only 'DEPOSIT' and 'SETTLEMENT' are allowed.")
            if trx_type == 'DEPOSIT':
                self.type_of_journal = 'deposit'
            elif trx_type == 'SETTLEMENT':
                self.type_of_journal = 'sattlement'  
                
        except pd.errors.EmptyDataError:
            raise ValidationError("The uploaded file is empty or corrupted.")
        except Exception as e:
            raise ValidationError(f"Error processing file: {str(e)}")

    def _generate_mail_body(self):
        body = ""
        if self.deposit_history_id.state == 'failed':
            status = "Gagal"
            body = Markup(f"""
                <p><strong>Subject:</strong> Hasil Impor Deposit Settlement – Ref: {self.deposit_history_id.name}</p>
                <p>Yth. Tim,</p>
                <p>Berikut hasil impor Deposit Settlement:</p>
                <ul>
                    <li><strong>Referensi:</strong> {self.deposit_history_id.name}</li>
                    <li><strong>Tanggal:</strong> {self.created_date.strftime("")}</li>
                    <li><strong>Diproses oleh:</strong> {self.created_user_id.name}</li>
                    <li><strong>Status:</strong> {status}</li>
                    <li><strong>Total Data:</strong> 0</li>
                    
                </ul>
                <p>Silakan cek log error di sistem untuk mengetahui penyebab kegagalan.</p>
                <br/>
                <p>Terima kasih,</p>
            """)
        elif self.deposit_history_id.state == 'success':
            status = "Berhasil"
            body = Markup(f"""
                <p><strong>Subject:</strong> Hasil Impor Deposit Settlement – Ref: {self.deposit_history_id.name}</p>
                <p>Yth. Tim,</p>
                <p>Berikut hasil impor Deposit Settlement:</p>
                <ul>
                    <li><strong>Referensi:</strong> {self.deposit_history_id.name}</li>
                    <li><strong>Tanggal:</strong> {self.created_date}</li>
                    <li><strong>Diproses oleh:</strong> {self.created_user_id.name}</li>
                    <li><strong>Status:</strong> {self.deposit_history_id.state}</li>
                    <li><strong>Total Data:</strong> {len(self.deposit_history_id.move_ids)}</li>
                    
                </ul>
                <p>Silahkan cek sistem untuk detail lebih lanjut</p>
                <br/>
                <p>Terima kasih,</p>
            """)
        return body


    def action_send_message(self):
        
        subject = f"Hasil Import Deposit Settlement – Ref:  {self.deposit_history_id.name}"
        recipient_ids = self.deposit_history_id.create_user_id.partner_id
        
        body = self._generate_mail_body()
        
        # body = html2plaintext(body)
        self.deposit_history_id.message_post(
            body=body,
            subject=subject,
            message_type='comment',
            subtype_xmlid='mail.mt_comment',
            partner_ids=recipient_ids.ids,
        )
    

    @api.model
    def import_data_from_excel(self):
        if not self.transaction_document:
            raise ValidationError("Please upload a file.")

        file_name = self.transaction_document_name.lower()
        file_content = base64.b64decode(self.transaction_document)
        error_messages = []
        move_ids = []
        # history_name = self.get_custom_deposit_sequence()
        history_name = self.env['ir.sequence'].next_by_code('deposit.settlement.history')
        deposit_history_id = self.env['deposit.settlement.history'].create({
            'name': history_name,
            'transaction_document': self.transaction_document,
            'transaction_document_name': self.transaction_document_name,
            'create_user_id': self.created_user_id.id,
            'create_date': fields.Datetime.now() + timedelta(hours=7),
            'type_of_journal': self.type_of_journal,
            
        })

        # Parse the Excel/CSV file into a DataFrame
        decoded_file = base64.b64decode(self.transaction_document)

        if file_name.endswith('.csv'):
            df = pd.read_csv(io.BytesIO(decoded_file),skipinitialspace=True)
        elif file_name.endswith(('.xls', '.xlsx')):
            df = pd.read_excel(io.BytesIO(decoded_file))
            
        # Trim whitespace from all string columns
        df.columns = df.columns.str.replace(' ', '', regex=False)
        
        # Get required configuration values
        sequence = self.env['ir.sequence']
        journal_id = self.env['account.journal']
        debit_account_id = self.env['account.account']
        credit_account_id = self.env['account.account']
        
        if self.type_of_journal == 'deposit':
            journal_id = self.env.company.journal_deposit_id
            debit_account_id = False
            credit_account_id = self.env.company.account_credit_deposit_id
        elif self.type_of_journal == 'sattlement':
            journal_id = self.env.company.journal_settlement_id
            debit_account_id = self.env.company.account_debit_settlement_id
            credit_account_id = False
        
        # # Ensure all required configurations are available
        # if not journal_id or not debit_account_id or not credit_account_id:
        #     raise ValidationError("Please configure the journal and accounts in company settings.")

        if self.type_of_journal == 'deposit':
            if not journal_id:
                raise ValidationError("Please configure the deposit journal in company settings.")
            if not credit_account_id:
                raise ValidationError("Please configure the credit account for deposits in company settings.")
        elif self.type_of_journal == 'sattlement':
            if not journal_id:
                raise ValidationError("Please configure the settlement journal in company settings.")
            if not debit_account_id:
                raise ValidationError("Please configure the debit account for settlements in company settings.")
        
        # Process data and convert to JSON
        records = []
        no = 2
        for index, row in df.iterrows():
            # Convert transaction date to proper format
            transaction_date = row.get('TRANSACTION_DATE')
            
            # Handle different date formats
            if isinstance(transaction_date, str):
                try:
                    # Try to parse as "1-Mar-24" format
                    transaction_date = datetime.strptime(transaction_date.strip(), "%d-%b-%y").date()
                except ValueError:
                    try:
                        # Try to parse as "2024-03-01" format
                        transaction_date = datetime.strptime(transaction_date.strip(), "%Y-%m-%d").date()
                    except ValueError:
                        # Default to today if parsing fails
                        transaction_date = datetime.today().date()
            elif isinstance(transaction_date, pd.Timestamp):
                transaction_date = transaction_date.date()
            else:
                transaction_date = datetime.today().date()
            
            # Generate unique sequence for move name
            move_name = sequence.next_by_code('journal.deposit' if self.type_of_journal == 'deposit' else 'journal.settlement')
            
            # Get amount value, default to 0 if not present
            amount = float(row.get('DEFAULT_AMOUNT', 0.0))
            if amount <= 0:
                continue  # Skip entries with zero or negative amounts
            
            # Create record with all necessary data
            records.append({
                'no': no,
                'name': move_name,
                'date': transaction_date.strftime('%Y-%m-%d'),
                'journal_id': journal_id.id,
                'debit_account_id': debit_account_id.id if self.type_of_journal == 'sattlement' else 0,
                'credit_account_id': credit_account_id.id if self.type_of_journal == 'deposit' else 0,
                'amount': amount,
                'ref': deposit_history_id.name,
                'state': 'posted',
                'posted_before': True,
                'currency_id': self.env.company.currency_id.id,
                'company_currency_id': self.env.company.currency_id.id,
                'company_id': self.env.company.id,
                'bank_account_name': str(row.get('BANK_ACCOUNT_NAME', '')),
                'custodian_bank_name': str(row.get('CUSTODIAN_BANK_NAME', '')),
                'transaction_number': str(row.get('TRANSACTION_NUMBER', '')),
                'org_short_code': str(row.get('ORG_SHORT_CODE', '')),
                'organization_name': str(row.get('ORGANIZATION_NAME', '')),
                'completion_date': str(row.get('COMPLETION_DATE', '')),
                'bank_statement_date': str(row.get('BANK_STATEMENT_DATE', '')),
                'default_currency': str(row.get('DEFAULT_CURRENCY', '')),
                'event_type_code': str(row.get('EVENT_TYPE_CODE', '')),
            })
            no += 1
        
        if not records:
            raise ValidationError("No valid records found in the file.")
        
        # Convert records to JSON
        json_data = json.dumps(records)
        
        # Create or replace the stored procedure
        self.create_stored_procedure()
        
        # Use the with statement for savepoint management
        move_ids = []
        failed_entries = []
        
        # Store procedure execution in a savepoint context
        with self.env.cr.savepoint():
            # Call the stored procedure with JSON data
            self.env.cr.execute("SELECT process_deposit_settlement_json(%s, %s, %s)", 
                            (json_data, self.env.uid, self.type_of_journal))
            
            # Get result from procedure
            datas = self.env.cr.dictfetchall()
            result = False
            if datas and 'process_deposit_settlement_json' in datas[0]:
                result = datas[0]['process_deposit_settlement_json']
            
            # Parse success and failure data
            if result and 'success' in result:
                if 'move_ids' in result['success'] and result['success']['move_ids']:
                    move_ids = [int(item) for item in result['success']['move_ids']]
                    
            if result and 'failed_entries' in result and result['failed_entries']:
                failed_entries = result['failed_entries']
        
        # This code runs if no exception occurred (success) OR after exception handling (failure)
        if failed_entries:            
            # Format error message for failed entries
            error_lines = []
            for entry in failed_entries:
                row_no = entry.get('entry_no')
                ref = entry.get('transaction_number') or f"Entry #{entry.get('entry_no')}"
                bank = entry.get('bank_account_name') or 'N/A'
                error = entry.get('error') or 'Unknown error'
                error_lines.append(f"• in row {row_no}, {ref} (Bank: {bank}): {error}")
            
            error_message = _('Import failed. All entries were rolled back due to the following errors:\n{}').format('\n'.join(error_lines))
            deposit_history_id.write({'state': 'failed', 'note': error_message})
            self.deposit_history_id = deposit_history_id.id
            self.action_send_message()
            self.env.cr.commit()  # commit the history update

            raise ValidationError(error_message)
        
        # If we got here, all entries were successful
        deposit_history_id.write({
            'state': 'success',
            'move_ids': [(6, 0, move_ids)],
            'note': _('Import successful. All entries were created successfully.'),
        })
        self.deposit_history_id = deposit_history_id.id
        self.action_send_message()
    
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Success'),
                'message': _(f'{len(move_ids)} journal entries created successfully.'),
                'sticky': False,
                'type': 'success',
                'next': {'type': 'ir.actions.act_window_close'},
            }
        }

    def create_stored_procedure(self):
        """Create or replace PostgreSQL stored procedure for processing deposit settlement JSON"""
        self.env.cr.execute("""
        DROP FUNCTION process_deposit_settlement_json(text,integer,text);
        CREATE OR REPLACE FUNCTION process_deposit_settlement_json(
            data_json TEXT,
            user_id INTEGER,
            journal_type TEXT
        ) RETURNS JSONB AS $$
        DECLARE
            rec JSON;
            data_records JSON;
            move_id INTEGER;
            move_ids INTEGER[] := '{}';
            failed_entries JSONB[] := '{}';
            result_json JSONB;
            company_id INTEGER;
            currency_id INTEGER;
            debit_account_id INTEGER;
            credit_account_id INTEGER;
            actual_debit_account_id INTEGER;
            actual_credit_account_id INTEGER;
            found_account_id INTEGER;
            bank_account_name TEXT;
            entry_no INTEGER;
            transaction_number TEXT;
            account_error BOOLEAN;
            error_message TEXT;
        BEGIN
            -- Parse the JSON data
            data_records := data_json::JSON;
            
            -- Process each record
            FOR rec IN SELECT value FROM json_array_elements(data_records)
            LOOP
                account_error := FALSE;
                error_message := '';
                
                -- Get reference values for error reporting
                entry_no := (rec->>'no')::INTEGER;
                transaction_number := rec->>'transaction_number';
                bank_account_name := rec->>'bank_account_name';
                
                -- Get debit and credit account values
                debit_account_id := (rec->>'debit_account_id')::INTEGER;
                credit_account_id := (rec->>'credit_account_id')::INTEGER;
                
                -- Handle debit account lookup if needed
                actual_debit_account_id := debit_account_id;
                IF debit_account_id = 0 AND bank_account_name IS NOT NULL AND bank_account_name != '' THEN
                    SELECT aj.default_account_id INTO found_account_id
                    FROM account_journal aj
                    JOIN res_partner_bank rpb ON aj.bank_account_id = rpb.id
                    WHERE rpb.acc_number = bank_account_name
                    LIMIT 1;
                    
                    IF found_account_id IS NOT NULL THEN
                        actual_debit_account_id := found_account_id;
                    ELSE
                        account_error := TRUE;
                        error_message := error_message || 'Debit account not found for bank account: ' || bank_account_name;
                    END IF;
                ELSIF debit_account_id = 0 THEN
                    account_error := TRUE;
                    error_message := error_message || 'Missing debit account and no bank account specified';
                END IF;
                
                -- Handle credit account lookup if needed
                actual_credit_account_id := credit_account_id;
                IF credit_account_id = 0 AND bank_account_name IS NOT NULL AND bank_account_name != '' THEN
                    SELECT aj.default_account_id INTO found_account_id
                    FROM account_journal aj
                    JOIN res_partner_bank rpb ON aj.bank_account_id = rpb.id
                    WHERE rpb.acc_number = bank_account_name
                    LIMIT 1;
                    
                    IF found_account_id IS NOT NULL THEN
                        actual_credit_account_id := found_account_id;
                    ELSE
                        account_error := TRUE;
                        IF error_message != '' THEN
                            error_message := error_message || ' and ';
                        END IF;
                        error_message := error_message || 'Credit account not found for bank account: ' || bank_account_name;
                    END IF;
                ELSIF credit_account_id = 0 THEN
                    account_error := TRUE;
                    IF error_message != '' THEN
                        error_message := error_message || ' and ';
                    END IF;
                    error_message := error_message || 'Missing credit account and no bank account specified';
                END IF;
                
                -- Check if we should skip this entry due to account issues
                IF account_error THEN
                    failed_entries := array_append(failed_entries, jsonb_build_object(
                        'entry_no', entry_no,
                        'transaction_number', transaction_number,
                        'bank_account_name', bank_account_name,
                        'error', error_message
                    ));
                    CONTINUE;
                END IF;
                
                -- Insert account_move record
                INSERT INTO account_move (
                    name,
                    date,
                    invoice_date,
                    journal_id,
                    company_id,
                    currency_id,
                    move_type,
                    state,
                    ref,
                    posted_before,
                    deposit_settlement,
                    auto_post,
                    create_uid,
                    create_date,
                    write_uid,
                    write_date,
                    bank_account_name,
                    custodian_bank_name,
                    transaction_number,
                    org_short_code,
                    organization_name,
                    completion_date,
                    bank_statement_date,
                    default_currency,
                    event_type_code,
                    amount_total_signed,
                    amount_total
                ) VALUES (
                    rec->>'name',
                    (rec->>'date')::DATE,
                    (rec->>'date')::DATE,
                    (rec->>'journal_id')::INTEGER,
                    (rec->>'company_id')::INTEGER,
                    (rec->>'currency_id')::INTEGER,
                    'entry',
                    rec->>'state',
                    rec->>'ref',
                    TRUE,
                    TRUE, 
                    'no',
                    user_id,
                    NOW(),
                    user_id,
                    NOW(),
                    rec->>'bank_account_name',
                    rec->>'custodian_bank_name',
                    rec->>'transaction_number',
                    rec->>'org_short_code',
                    rec->>'organization_name',
                    rec->>'completion_date',
                    rec->>'bank_statement_date',
                    rec->>'default_currency',
                    rec->>'event_type_code',
                    (rec->>'amount')::NUMERIC,
                    (rec->>'amount')::NUMERIC
                ) RETURNING id INTO move_id;
                
                -- Add to move_ids array
                move_ids := array_append(move_ids, move_id);
                
                -- Insert debit line
                INSERT INTO account_move_line (
                    move_id,
                    journal_id,
                    display_type,
                    account_id,
                    name,
                    parent_state,
                    date,
                    invoice_date,
                    amount_currency,
                    debit,
                    credit,
                    balance,
                    company_id,
                    currency_id,
                    company_currency_id,
                    create_uid,
                    create_date,
                    write_uid,
                    write_date,
                    deposit_settlement
                ) VALUES (
                    move_id,
                    (rec->>'journal_id')::INTEGER,
                    'product',
                    actual_debit_account_id,
                    'Debit: ' || COALESCE(rec->>'transaction_number', 'Deposit/Settlement Entry'),
                    rec->>'state',
                    (rec->>'date')::DATE,
                    (rec->>'date')::DATE,
                    (rec->>'amount')::NUMERIC,
                    (rec->>'amount')::NUMERIC,
                    0,
                    (rec->>'amount')::NUMERIC,
                    (rec->>'company_id')::INTEGER,
                    (rec->>'currency_id')::INTEGER,
                    (rec->>'company_currency_id')::INTEGER,
                    user_id,
                    NOW(),
                    user_id,
                    NOW(),
                    CASE WHEN journal_type = 'deposit' THEN TRUE ELSE FALSE END
                );
                
                -- Insert credit line
                INSERT INTO account_move_line (
                    move_id,
                    journal_id,
                    display_type,
                    account_id,
                    name,
                    parent_state,
                    date,
                    invoice_date,
                    amount_currency,
                    debit,
                    credit,
                    balance,
                    company_id,
                    currency_id,
                    company_currency_id,
                    create_uid,
                    create_date,
                    write_uid,
                    write_date,
                    deposit_settlement
                ) VALUES (
                    move_id,
                    (rec->>'journal_id')::INTEGER,
                    'product',
                    actual_credit_account_id,
                    'Credit: ' || COALESCE(rec->>'transaction_number', 'Deposit/Settlement Entry'),
                    rec->>'state',
                    (rec->>'date')::DATE,
                    (rec->>'date')::DATE,
                    -(rec->>'amount')::NUMERIC,
                    0,
                    (rec->>'amount')::NUMERIC,
                    -(rec->>'amount')::NUMERIC,
                    (rec->>'company_id')::INTEGER,
                    (rec->>'currency_id')::INTEGER,
                    (rec->>'company_currency_id')::INTEGER,
                    user_id,
                    NOW(),
                    user_id,
                    NOW(),
                    CASE WHEN journal_type = 'sattlement' THEN TRUE ELSE FALSE END
                );
            END LOOP;
            
            -- Build composite result with both success and failure information
            result_json := jsonb_build_object(
                'success', jsonb_build_object(
                    'move_ids', array_to_json(move_ids)
                ),
                'failed_entries', array_to_json(failed_entries)
            );
            
            -- Return the result object
            RETURN result_json;
        END;
        $$ LANGUAGE plpgsql;
        """)
        self.env.cr.commit()

    def button_template_import_deposit_sattlement(self):
        file_name = 'Template Deposit Sattlement.xlsx'
        output = io.BytesIO()
        workbook = xlsxwriter.Workbook(output, {'in_memory': True})
        sheet = workbook.add_worksheet()
        format1 = workbook.add_format(
            {'font_size': 14, 'align': 'center', 'left': 1, 'bottom': 1, 'right': 1, 'top': 1, 'valign': 'vcenter',
             'bold': True, 'border': True})
        sheet.set_column('A:AD', 30.15)
        column_header = ["TRANSACTION NUMBER", "TRANSACTION DATE", "TRANSACTION TIME", "ORG SHORT CODE", "ORGANIZATION NAME", "DEFAULT AMOUNT", "CUSTODIAN BANK NAME", "BANK ACCOUNT", "JOURNAL NUMBER", "COMPLETION DATE", "COMPLETION TIME", "BANK STATEMENT TIME", "DEFAULT CURRENCY", "EVENT TYPE CODE", "BANK ACCOUNT NAME", "TRX TYPE", "TRX SUB TYPE", "RECONCILIATION REFERENCE", "TRANSACTION DETAILS", "REASON TYPE", "LINE ATTRIBUTE1", "LINE ATTRIBUTE2", "LINE ATTRIBUTE3", "TRIBE", "PRODUCT", "TRANSACTION REVERSAL FLAG"]

        col = 0
        for c in column_header:
            sheet.write(0, col, c, format1)
            col += 1
        
        workbook.close()
        output.seek(0)
        result = base64.b64encode(output.read())
        attachment_id = self.env['ir.attachment'].create({
            'name': file_name,
            'store_fname': file_name,
            'datas': result,
        })
        output.close()

        action = {
            'type': 'ir.actions.act_url',
            'url': '/web/content/{}?download=true'.format(attachment_id.id, ),
            'target': 'self'
        }
        return action
