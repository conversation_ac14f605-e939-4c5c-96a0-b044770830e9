<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

         <!-- Formview Negotiation-->
        <record id="ap_negotiation_view_form" model="ir.ui.view">
            <field name="name">Negotiation</field>
            <field name="model">bidding.negotiation</field>
            <field name="arch" type="xml">
                <form string="Negotiation">
                    <header>
                        <button name="action_submit" string="Submit" type="object" class="oe_highlight"
                                invisible="state != 'draft'"/>
                        <button name="action_check_status_vendor" string="Check Status Vendor" type="object" invisible="state != 'due_dillgence_approve'"/>
                        <button name="print_negotiation" string="Print Negotiation" type="object" class="oe_highlight" invisible="state != 'finish'"/>
                        <!-- <button name="action_award" string="Award" type="object" class="oe_highlight"
                                invisible="state != 'submit'"/> -->
                        <button name="action_finish" string="Finish" type="object" class="oe_highlight"
                                invisible="state != 'award_approve'"/>
                        <button name="action_award" string="Award" type="object" class="oe_highlight"  invisible="state != 'submit'"/>
                        <!-- <button name="action_negotiation_send" invisible="state != 'draft'" string="Send by Email" type="object" context="{'send_negotiation':True}" class="oe_highlight" data-hotkey="g"/> -->
                        <button name="action_draft" string="Set to Draft" type="object" invisible="state in ('submit','cancel')"/>
                        <button name="action_cancel" string="Cancel" type="object" invisible="state in ('cancel','confirm')"/>
                        <field name="state" widget="statusbar" statusbar_visible="draft,submit,pending_approve,due_dillgence_approve,award_approve,finish"/>
                    </header>
                    <sheet>
                        <div class="oe_button_box" name="button_box"/>
                        <div class="oe_title">
                            <h1><field name="name" readonly="1"/></h1>
                        </div>
                        <group>
                            <group>
                                <field name="bidding_id" required="1" readonly="state != 'draft'" domain="[('state', '=', 'start_bidding')]"/>
                                <field name="bidding_description" readonly="1" force_save="1"/>
                                <field name="bidding_type" readonly="1" force_save="1"/>
                                <field name="is_add_product_negotiation" invisible="1" force_save="1"/>
                                <field name="partner_id" required="1" readonly="state != 'draft'"/>
                                <field name="available_partner_ids" widget="many2many_tags" invisible="1"/>
                                <field name="partner_reference" string="Quotation Number" required="0" readonly="state != 'draft'"/>
                                <field name="buyer_id" readonly="1" force_save="1"/>
                                <field name="requestor_id" readonly="1" force_save="1"/>
                            </group>
                            <group>
                                <field name="negotiation_date" invisible="1"/>
                                <field name="submitted_date" required="state == 'submit'" readonly="1"/>
                                <field name="vendor_status" readonly="1" force_save="1" invisible="vendor_status in [False]"/>
                                <field name="due_diligence_status" readonly="1" force_save="1" invisible="vendor_status in [False]"/>
                                <field name="attachment_ids" widget="many2many_binary" readonly="state != 'draft'" required="1"/>
                                <field name="unit_id" readonly="1" force_save="1"/>
                                <field name="group_id" readonly="1" force_save="1"/> 
                                <field name="payment_term_id" readonly="state != 'draft'" options="{'no_create': True}" required='1' />
                                <field name="exchange_rate" invisible="1" force_save="1"/> 
                            </group>
                        </group>
                        <notebook>
                        <page string="Products">
                            <field name="line_ids" readonly="state != 'draft'" invisible="is_add_product_negotiation == True or state == 'submit'">
                                <list editable="bottom" create="false" delete="true">
                                    <control>
                                        <create name="add_product_control" string="Add a product"/>
                                    </control>
                                    <field name="product_tmpl_id" required="1"/>
                                    <field name="product_variant_id" domain="[('product_tmpl_id', '=', product_tmpl_id), ('is_product_variant', '=', True)]" required="0" width="150px"/>
                                    <field name="propose_variant" required="0" width="130px"/>
                                    <field name="description" required="1"/>
                                    <field name="quantity" required="1"/>
                                    <field name="product_uom_id" required="1"/>
                                    <field name="is_percentage" force_save="1" width="130px"/>
                                    <field name="uom_type" required="0" column_invisible="1"/>
                                    <field name="currency_id" required="is_percentage != True" readonly="state != 'draft'"/>
                                    <field name="company_currency_id" readonly="1" column_invisible="1"/>
                                    <field name="company_id" readonly="1" column_invisible="1"/>
                                    <field name="unit_price" readonly="is_percentage == True or state != 'draft'" width="150px" force_save="True"/>
                                    <field name="is_company_currency" column_invisible="1"/>
                                    <field name="exchange_rate" invisible="is_company_currency == True" readonly="state != 'draft'" width="150px" force_save="1"/>
                                    <field name="is_manajemen_fee" invisible="is_percentage == True" width="130px"/>
                                    <field name="total" readonly="1"/>
                                    <field name="is_award_line" width="130px" column_invisible="parent.state == 'draft'" />
                                    <field name="state" column_invisible="1"/>
                                </list>
                                <form string="Products">
                                    <field name="product_tmpl_id" required="1"/>
                                    <field name="product_variant_id" domain="[('product_tmpl_id', '=', product_tmpl_id), ('is_product_variant', '=', True)]" required="0"/>
                                    <field name="propose_variant" required="0" width="130px"/>
                                    <field name="description" required="1"/>
                                    <group>
                                        <group>
                                            <field name="quantity" required="1"/>
                                            <field name="product_uom_id" required="1"/>
                                            <field name="is_percentage" force_save="1"/>
                                            <field name="uom_type" required="0" invisible="1"/>
                                            <field name="currency_id" required="1"/>
                                            <field name="company_currency_id" readonly="1" invisible="1"/>
                                            <field name="company_id" readonly="1" invisible="1"/>
                                            <field name="unit_price" required="1"/>
                                            <field name="exchange_rate"/>
                                            <field name="is_manajemen_fee" invisible="is_percentage == True"/>
                                            <field name="is_award_line" />
                                        </group>
                                        <group>
                                            <field name="total" readonly="1"/>
                                        </group>
                                    </group>
                                </form>
                            </field>

                            <field name="line_ids" readonly="state != 'draft'" invisible="is_add_product_negotiation != True or state == 'submit'">
                                <list editable="bottom" create="true" delete="true">
                                    <control>
                                        <create name="add_product_control" string="Add a product"/>
                                    </control>
                                    <field name="product_tmpl_id" required="1"/>
                                    <field name="product_variant_id" domain="[('product_tmpl_id', '=', product_tmpl_id), ('is_product_variant', '=', True)]" required="0" width="150px"/>
                                    <field name="propose_variant" required="0" width="130px"/>
                                    <field name="description" required="1"/>
                                    <field name="quantity" required="1"/>
                                    <field name="product_uom_id" required="1"/>
                                    <field name="is_percentage" force_save="1" width="130px"/>
                                    <field name="uom_type" required="0" column_invisible="1"/>
                                    <field name="currency_id" required="is_percentage != True" readonly="state != 'draft'"/>
                                    <field name="company_currency_id" readonly="1" column_invisible="1"/>
                                    <field name="company_id" readonly="1" column_invisible="1"/>
                                    <field name="unit_price" readonly="is_percentage == True or state != 'draft'" width="150px" force_save="True"/>
                                    <field name="is_company_currency" column_invisible="1"/>
                                    <field name="exchange_rate" invisible="is_company_currency == True" readonly="state != 'draft'" width="150px" force_save="1"/>
                                    <field name="is_manajemen_fee" invisible="is_percentage == True" width="130px"/>
                                    <field name="total" readonly="1"/>
                                    <field name="is_award_line" width="130px" column_invisible="parent.state == 'draft'"/>
                                    <field name="state" column_invisible="1"/>
                                </list>
                                <form string="Products">
                                    <field name="product_tmpl_id" required="1"/>
                                    <field name="product_variant_id" domain="[('product_tmpl_id', '=', product_tmpl_id), ('is_product_variant', '=', True)]" required="0"/>
                                    <field name="propose_variant" required="0" width="130px"/>
                                    <field name="description" required="1"/>
                                    <group>
                                        <group>
                                            <field name="quantity" required="1"/>
                                            <field name="product_uom_id" required="1"/>
                                            <field name="is_percentage" force_save="1"/>
                                            <field name="uom_type" required="0" invisible="1"/>
                                            <field name="currency_id" required="1"/>
                                            <field name="company_currency_id" readonly="1" invisible="1"/>
                                            <field name="company_id" readonly="1" invisible="1"/>
                                            <field name="unit_price" required="1"/>
                                            <field name="exchange_rate"/>
                                            <field name="is_manajemen_fee" invisible="is_percentage == True"/>
                                            <field name="is_award_line"/>
                                        </group>
                                        <group>
                                            <field name="total" readonly="1"/>
                                        </group>
                                    </group>
                                </form>
                            </field>

                            <field name="line_ids" invisible="state != 'submit'">
                                <list editable="bottom" create="false" delete="true">
                                    <control>
                                        <create name="add_product_control" string="Add a product"/>
                                    </control>
                                    <field name="product_tmpl_id" required="1" readonly="state != 'draft'"/>
                                    <field name="product_variant_id" domain="[('product_tmpl_id', '=', product_tmpl_id), ('is_product_variant', '=', True)]" required="0" width="150px" readonly="state != 'draft'"/>
                                    <field name="propose_variant" required="0" width="130px"/>
                                    <field name="description" required="1" readonly="state != 'draft'"/>
                                    <field name="quantity" required="1" readonly="state != 'draft'"/>
                                    <field name="product_uom_id" required="1" readonly="state != 'draft'"/>
                                    <field name="is_percentage" force_save="1" width="130px" readonly="state != 'draft'"/>
                                    <field name="uom_type" required="0" column_invisible="1" readonly="state != 'draft'"/>
                                    <field name="currency_id" required="is_percentage != True" readonly="state != 'draft'"/>
                                    <field name="company_currency_id" readonly="1" column_invisible="1"/>
                                    <field name="company_id" readonly="1" column_invisible="1"/>
                                    <field name="unit_price" readonly="is_percentage == True or state != 'draft'" width="150px" force_save="True"/>
                                    <field name="is_company_currency" column_invisible="1"/>
                                    <field name="exchange_rate" invisible="is_company_currency == True" readonly="state != 'draft'" width="150px" force_save="1"/>
                                    <field name="is_manajemen_fee" invisible="is_percentage == True" width="130px"/>
                                    <field name="total" readonly="1"/>
                                    <field name="is_award_line" width="130px" readonly="state not in ('draft','submit')"/>
                                    <field name="state" column_invisible="1"/>
                                </list>
                                <form string="Products">
                                    <field name="product_tmpl_id" required="1"/>
                                    <field name="product_variant_id" domain="[('product_tmpl_id', '=', product_tmpl_id), ('is_product_variant', '=', True)]" required="0"/>
                                    <field name="propose_variant" required="0" width="130px"/>
                                    <field name="description" required="1"/>
                                    <group>
                                        <group>
                                            <field name="quantity" required="1"/>
                                            <field name="product_uom_id" required="1"/>
                                            <field name="is_percentage" force_save="1"/>
                                            <field name="uom_type" required="0" invisible="1"/>
                                            <field name="currency_id" required="1"/>
                                            <field name="company_currency_id" readonly="1" invisible="1"/>
                                            <field name="company_id" readonly="1" invisible="1"/>
                                            <field name="unit_price" required="1"/>
                                            <field name="exchange_rate"/>
                                            <field name="is_manajemen_fee" invisible="is_percentage == True"/>
                                            <field name="is_award_line"/>
                                        </group>
                                        <group>
                                            <field name="total" readonly="1"/>
                                        </group>
                                    </group>
                                </form>
                            </field>

                            <group>
                                <group>
                                    <field name="notes" placeholder="Notes..." nolabel="1" readonly="state != 'draft'"/>
                                </group>
                                <group class="oe_subtotal_footer">
                                    <field name="amount_total" nolabel="0" readonly="1"/>
                                </group>
                            </group>
                            <div class="clearfix"/>

                        </page>
                        <page string="Other Info">
                            <group>
                                <field name="delivery_location_id" readonly="state != 'draft'" required="1" domain="[('usage', '=', 'internal')]"/>
                                <field name="delivery_term_id" readonly="state != 'draft'" required="1"/>
                                <field name="company_id" readonly="1" required="1"/>
                            </group>
                        </page>

                    </notebook>
                </sheet>
                <chatter/>
                </form>
            </field>
        </record>

         <!-- Listview Negotiation-->
        <record id="ap_negotiation_view_list" model="ir.ui.view">
            <field name="name">Negotiation</field>
            <field name="model">bidding.negotiation</field>
            <field name="arch" type="xml">
                <list string="Negotiation" decoration-info="state == 'draft'"
                      decoration-muted="state == 'cancel'">
                      <header>
                        <button name="action_mass_award" type="object" string="Award"/>
                        <button name="action_open_bidding" type="object" string="Bidding Documents"/>
                    </header>
                    <field name="name" string="Negotiation Number"/>
                    <field name="negotiation_date"/> 
                    <field name="partner_id"/> 
                    <field name="amount_total" string="Total Bidding"/> 
                    <field name="state" string="Negotiation Status"/>
                    <field name="is_award" string="Award"/>
                </list>
            </field>
        </record>

         <!-- Search Negotiation-->
        <record id="ap_negotiation_view_search" model="ir.ui.view">
            <field name="name">bidding.negotiation.search</field>
            <field name="model">bidding.negotiation</field>
            <field name="arch" type="xml">
                <search string="Search Negotiation">
                    <separator string="General" />
                    <field name="name"/>
                    <field name="bidding_type"/>
                    <field name="state"/>

                    <separator string="Dates" />
                    <field name="negotiation_date"/>
                    <field name="submitted_date"/>

                    <separator string="Request Details" />
                    <field name="buyer_id"/>
                    <field name="requestor_id"/>
                    <field name="unit_id"/>
                    <field name="group_id"/>

                    <separator string="Filters" />
                    <filter name="state_draft" string="Draft" domain="[('state', '=', 'draft')]" />
                    <filter name="state_submit" string="Submit" domain="[('state', '=', 'submit')]" />
                    <filter name="state_award" string="Award" domain="[('state', '=', 'award')]" />
                    <filter name="state_due_dillgence_approve" string="Due Dillgence Approve" domain="[('state', '=', 'due_dillgence_approve')]" />
                    <filter name="state_award_approve" string="Award Approve" domain="[('state', '=', 'award_approve')]" />
                    <filter name="state_finish" string="Finish" domain="[('state', '=', 'finish')]" />
                    <filter name="state_cancel" string="Cancelled" domain="[('state', '=', 'cancel')]" />

                    <separator string="Grouping" />
                    <group expand="0" string="Group By">
                        <filter name="group_by_bidding" string="Bidding" domain="[]" context="{'group_by': 'bidding_id'}"/>
                        <filter name="group_by_bidding_type" string="Bidding Type" domain="[]" context="{'group_by': 'bidding_type'}"/>
                        <filter name="group_by_buyer_id" string="Buyer" domain="[]" context="{'group_by': 'buyer_id'}"/>
                        <filter name="group_by_requested_by" string="Requested By" domain="[]" context="{'group_by': 'requestor_id'}"/>
                        <filter name="group_by_unit" string="Unit" domain="[]" context="{'group_by': 'unit_id'}"/>
                        <filter name="group_by_group" string="Group" domain="[]" context="{'group_by': 'group_id'}"/>
                        <filter name="group_by_state" string="Status" domain="[]" context="{'group_by': 'state'}"/>
                    </group>
                </search>
            </field>
        </record>

        <!-- Action Negotiation-->
        <record id="action_negotiation" model="ir.actions.act_window">
            <field name="name">Negotiation</field>
            <field name="res_model">bidding.negotiation</field>
            <field name="view_mode">list,form</field>
            <field name="search_view_id" ref="ap_negotiation_view_search"/>
            <field name="view_ids" eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'list', 'view_id': ref('ap_negotiation_view_list')}),
                (0, 0, {'view_mode': 'form', 'view_id': ref('ap_negotiation_view_form')})]"/>
            <field name="domain">[]</field>
            <field name="context">{}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Click to add a new negotiation.
                </p>
            </field>
        </record>
    </data>
</odoo>