# -*- coding: utf-8 -*-
import logging
import json
import pytz
from datetime import datetime
from datetime import date as dt
import werkzeug.wrappers
from odoo.exceptions import AccessError
from odoo.addons.portal.controllers.portal import pager
from odoo import http


from werkzeug import Response
from odoo.http import request

_logger = logging.getLogger(__name__)


class WehaBiddingApi(http.Controller):

    @http.route('/api/v1/product_list', auth='public', methods=['GET'], csrf=False)
    def get_product_list(self, **kw):
        try:
            # Validate token
            headers = request.httprequest.headers
            token = str(headers.get('token', ''))
            if not token:
                return Response(
                    json.dumps({
                        'status': 'error',
                        'message': 'Required token!'
                    }),
                    content_type='application/json',
                    status=500
                )
            user_id = request.env['res.users'].sudo().search([('token', '=', token)], limit=1)
            if not user_id:
                return Response(
                    json.dumps({
                        'status': 'error',
                        'message': 'Invalid or expired token. Please authenticate again.'
                    }),
                    content_type='application/json',
                    status=500
                )
            
            # Check if token is expired
            tz_name = user_id.tz or 'UTC'
            tz = pytz.timezone(tz_name)
            now_user_tz = datetime.now(tz)
            now_user_tz = now_user_tz.strftime('%Y-%m-%d %H:%M:%S')
            
            if user_id.expired_token_date:
                expired_token_utc = user_id.expired_token_date.replace(tzinfo=pytz.utc)
                expired_token_tz = expired_token_utc.astimezone(tz)
                expired_token_tz = expired_token_tz.strftime('%Y-%m-%d %H:%M:%S')
                
                if str(expired_token_tz) < str(now_user_tz):
                    return Response(
                        json.dumps({
                            'status': 'error',
                            'message': 'Invalid or expired token. Please authenticate again.'
                        }),
                        content_type='application/json',
                        status=500
                    )


            # GET PRODUCT LIST
            # product_tmpl_id = kw.get('product_tmpl_id', '')
            # product_tmpl_name = kw.get('product_tmpl_name', '')
            # page = int(kw.get('page', ''))
            # if not page:
            #     return Response(
            #         json.dumps({
            #             'status': 'error',
            #             'message': 'Required page!'
            #         }),
            #         content_type='application/json',
            #         status=500
            #     )
            
            domain = []
            # if product_tmpl_id:
            #     domain.append(('product_tmpl_id','=', int(product_tmpl_id)))
            # if product_tmpl_name:
            #     domain.append(('product_tmpl_id.name','like', str(product_tmpl_name)))
            
            domain.append(('active','=', 'true'))
            # total_product_list = request.env['product.template'].sudo().search_count(domain)
            # page_detail = pager(
            #         url='/api/v1/product_list',
            #         total=total_product_list,
            #         page=page,
            #         step=5
            #     )
    
            # offset=page_detail['offset']
            result = request.env['product.template'].sudo().search_read(
                domain,
                order="id ASC",
                fields=['id','display_name','product_variant_ids','purchase_ok']
            )
            # _logger.info(result)
            data = {
                'status': 'success',
                'data': result
            }
            headers = {'Content-Type': 'application/json'}
            return Response(json.dumps(data,indent=4, sort_keys=True, default=str), headers=headers)

        except Exception as e:
            return Response(
                json.dumps({
                    'status': 'error',
                    'message': str(e)
                }),
                headers={'Content-Type': 'application/json'},
                status=500
            )

    
    @http.route('/api/v1/product_variant_list', auth='public', methods=['GET'], csrf=False)
    def get_product_variant_list(self, **kw):
        try:
            # Validate token
            headers = request.httprequest.headers
            token = str(headers.get('token', ''))
            if not token:
                return Response(
                    json.dumps({
                        'status': 'error',
                        'message': 'Required token!'
                    }),
                    content_type='application/json',
                    status=500
                )
            user_id = request.env['res.users'].sudo().search([('token', '=', token)], limit=1)
            if not user_id:
                return Response(
                    json.dumps({
                        'status': 'error',
                        'message': 'Invalid or expired token. Please authenticate again.'
                    }),
                    content_type='application/json',
                    status=500
                )
            
            # Check if token is expired
            tz_name = user_id.tz or 'UTC'
            tz = pytz.timezone(tz_name)
            now_user_tz = datetime.now(tz)
            now_user_tz = now_user_tz.strftime('%Y-%m-%d %H:%M:%S')
            
            if user_id.expired_token_date:
                expired_token_utc = user_id.expired_token_date.replace(tzinfo=pytz.utc)
                expired_token_tz = expired_token_utc.astimezone(tz)
                expired_token_tz = expired_token_tz.strftime('%Y-%m-%d %H:%M:%S')
                
                if str(expired_token_tz) < str(now_user_tz):
                    return Response(
                        json.dumps({
                            'status': 'error',
                            'message': 'Invalid or expired token. Please authenticate again.'
                        }),
                        content_type='application/json',
                        status=500
                    )

            # page = int(kw.get('page', ''))
            # if not page:
            #     return Response(
            #         json.dumps({
            #             'status': 'error',
            #             'message': 'Required page!'
            #         }),
            #         content_type='application/json',
            #         status=500
            #     )

            # limit = kw.get('limit', '')
            # if not limit:
            #     return Response(
            #         json.dumps({
            #             'status': 'error',
            #             'message': 'Required limit!'
            #         }),
            #         content_type='application/json',
            #         status=500
            #     )

            domain = []
            # if product_variant_id:
            #     domain.append(('product_variant_id','=', int(product_variant_id)))
            # if product_variant_name:
            #     domain.append(('product_variant_id.name','like', str(product_variant_name)))
            # if product_tmpl_id:
            #     domain.append(('product_tmpl_id','=', int(product_tmpl_id)))

            domain.append(('active','=', 'true'))
            # total_product_variant_list = request.env['product.product'].sudo().search_count(domain)
            # page_detail = pager(
            #         url='/api/v1/product_list',
            #         total=total_product_variant_list,
            #         page=page,
            #         step=5
            #     )
            result = request.env['product.product'].sudo().search_read(domain, order="id ASC", fields=['id','display_name','product_tmpl_id'])

            data = {
                'status': 'success',
                'data': result
            }
            #return valid_response(data)

            headers = {'Content-Type': 'application/json'}
            return Response(json.dumps(data,indent=4, sort_keys=True, default=str), headers=headers)
        except Exception as e:
            return Response(
                json.dumps({
                    'status': 'error',
                    'message': str(e)
                }),
                headers={'Content-Type': 'application/json'},
                status=500
            )


    @http.route('/api/v1/bank_master', auth='public')
    def get_bank_master(self, **kw):
        try:
            # Validate token
            headers = request.httprequest.headers
            token = str(headers.get('token', ''))
            if not token:
                return Response(
                    json.dumps({
                        'status': 'error',
                        'message': 'Required token!'
                    }),
                    content_type='application/json',
                    status=500
                )
            user_id = request.env['res.users'].sudo().search([('token', '=', token)], limit=1)
            if not user_id:
                return Response(
                    json.dumps({
                        'status': 'error',
                        'message': 'Invalid or expired token. Please authenticate again.'
                    }),
                    content_type='application/json',
                    status=500
                )

            tz_name = user_id.tz or 'UTC'
            tz = pytz.timezone(tz_name)
            now_user_tz = datetime.now(tz)
            now_user_tz = now_user_tz.strftime('%Y-%m-%d %H:%M:%S')
            
            if user_id.expired_token_date:
                expired_token_utc = user_id.expired_token_date.replace(tzinfo=pytz.utc)
                expired_token_tz = expired_token_utc.astimezone(tz)
                expired_token_tz = expired_token_tz.strftime('%Y-%m-%d %H:%M:%S')
                
                if str(expired_token_tz) < str(now_user_tz):
                    return Response(
                        json.dumps({
                            'status': 'error',
                            'message': 'Invalid or expired token. Please authenticate again.'
                        }),
                        content_type='application/json',
                        status=500
                    )
            # domian = []
            # domain.append(('active','=', 'true'))

            # result = request.env['res.bank'].sudo().search_read(domain, order="id ASC", fields=['id','name'])
            query = """
                SELECT 
                id, name
                FROM res_bank
            """
            request.env.cr.execute(query)
            result = request.env.cr.dictfetchall()  # List of dicts
            _logger.info(str(result))
            data = {
                'status': 'success',
                'data': result
            }
            #return valid_response(data)

            headers = {'Content-Type': 'application/json'}
            return Response(json.dumps(data,indent=4, sort_keys=True, default=str), headers=headers)
        except Exception as e:
            return Response(
                json.dumps({
                    'status': 'error',
                    'message': str(e)
                }),
                headers={'Content-Type': 'application/json'},
                status=500
            )
            