<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <data>
    
        <record id="inherit_view_wizard_mass_asset_generate_id_inherit_account_asset_extension" model="ir.ui.view">
            <field name="name">wizard.mass.asset.generate.view.form.inherit</field>
            <field name="model">wizard.mass.asset.generate</field>
            <field name="inherit_id" ref="account_asset_extension.view_wizard_mass_asset_generate"/>
            <field name="arch" type="xml">
                <xpath expr="//list/field[@name='company_id']" position="attributes">
                    <attribute name="column_invisible" >1</attribute>
                </xpath>
                <xpath expr="//list/field[@name='date_picking']" position="attributes">
                    <attribute name="optional" >hide</attribute>
                    <attribute name="readonly" >1</attribute>
                    <attribute name="force_save">1</attribute>
                </xpath>
                <xpath expr="//list/field[@name='purchase_line_number']" position="replace"/>
<!--                    <attribute name="optional" >hide</attribute>-->
<!--                    <attribute name="readonly" >1</attribute>-->
<!--                    <attribute name="invisible" >1</attribute>-->
                <!-- </xpath> -->
                <xpath expr="//list/field[@name='date_picking']" position="attributes">
                    <attribute name="optional" >hide</attribute>
                    <attribute name="readonly" >1</attribute>
                    <attribute name="force_save">1</attribute>
                </xpath>
                <xpath expr="//list/field[@name='analytic_account_id']" position="attributes">
                    <attribute name="column_invisible" >1</attribute>
                    <attribute name="required" >0</attribute>
                </xpath>
                <xpath expr="//list/field[@name='invoice_line_id']" position="replace"/>
<!--                    <attribute name="optional" >hide</attribute>-->
<!--                    <attribute name="readonly" >1</attribute>-->
<!--                    <attribute name="invisible" >1</attribute>-->
<!--                    <attribute name="required" >0</attribute>-->
                <!-- </xpath> -->
                <xpath expr="//list/field[@name='line_number']" position="replace"/>
<!--                    <attribute name="optional" >hide</attribute>-->
<!--                    <attribute name="readonly" >1</attribute>-->
<!--                    <attribute name="invisible" >1</attribute>-->
                <!-- </xpath> -->
                <xpath expr="//list/field[@name='model_id']" position="attributes">
                    <attribute name="string">Asset Major</attribute>
                    <attribute name="readonly">not fpjp_id</attribute>
                    <attribute name="required">fpjp_id</attribute>
                    <attribute name="force_save">1</attribute>
                </xpath>
                <xpath expr="//field[@name='amount_total']" position="after">
<!--                    <field name="stock_location_id" invisible="type != 'merge'" required="type == 'merge'"/>-->
                    <field name="asset_location_id" invisible="type != 'merge'" required="type == 'merge'"/>
                </xpath>
                <xpath expr="//list/field[@name='product_code']" position="replace">
<!--                    <attribute name="invisible" >1</attribute>-->
                    <field name="stock_move_id" invisible="1"/>
                </xpath>
                <xpath expr="//field[@name='purchase_ids']" position="attributes">
                    <attribute name="invisible" >1</attribute>
                </xpath>
                <xpath expr="//field[@name='company_id']" position="attributes">
                    <attribute name="invisible" >1</attribute>
                </xpath>
                <xpath expr="//field[@name='date']" position="attributes">
                    <attribute name="readonly" >1</attribute>
                    <attribute name="force_save" >1</attribute>
                </xpath>
                <xpath expr="//field[@name='purchase_ids']" position="after">
                    <field name="major_id" invisible="type != 'merge'" required="type == 'merge'" domain="[('state', '=', 'model')]"/>
                </xpath>
                <xpath expr="//field[@name='is_selected_all']" position="replace">
                    <field name="is_selected_all"/>
                </xpath>
                <xpath expr="//list/field[@name='selected']" position="replace">
                    <field name="selected"/>
                </xpath>

                <xpath expr="//list/field[@name='amount']" position="before">
                    <field name="currency_id" column_invisible="1"/>
                    <field name="currency_rate" column_invisible="1"/>
                </xpath>
                <xpath expr="//list/field[@name='amount']" position="attributes">
                    <attribute name="readonly">1</attribute>
                    <attribute name="required">0</attribute>
                    <attribute name="force_save">1</attribute>
                </xpath>
                <xpath expr="//list/field[@name='purchase_id']" position="attributes">
                    <attribute name="readonly">1</attribute>
                    <attribute name="force_save">1</attribute>
                </xpath>
                <xpath expr="//list/field[@name='picking_id']" position="attributes">
                    <attribute name="readonly">1</attribute>
                    <attribute name="force_save">1</attribute>
                </xpath>
                <xpath expr="//list/field[@name='picking_id']" position="after">
                    <field name="fpjp_id" readonly="1" force_save="1"/>
                    <field name="fpjp_line_id" readonly="1" force_save="1" column_invisible="1"/>
                    <field name="desc_fpjp" readonly="1" force_save="1"/>
                </xpath>
                <xpath expr="//list/field[@name='product_id']" position="attributes">
                    <attribute name="readonly">1</attribute>
                    <attribute name="force_save">1</attribute>
                </xpath>
                <xpath expr="//list/field[@name='product_id']" position="after">
                    <field name="product_tmpl_id" readonly="1" force_save="1" optional="show"/>
                    <field name="asset_location_id" />
                </xpath>
                <xpath expr="//list/field[@name='product_id']" position="attributes">
                    <attribute name="column_invisible">1</attribute>
                </xpath>
                <xpath expr="//list/field[@name='stock_move_id']" position="attributes">
                    <attribute name="column_invisible">1</attribute>
                </xpath>
                <xpath expr="//list/field[@name='invoice_id']" position="attributes">
                    <attribute name="readonly">1</attribute>
                    <attribute name="required">0</attribute>
                    <attribute name="force_save">1</attribute>
                </xpath>
                <xpath expr="//list/field[@name='qty']" position="attributes">
                    <attribute name="readonly">1</attribute>
                    <attribute name="required">0</attribute>
                    <attribute name="force_save">1</attribute>
                </xpath>
                <xpath expr="//field[@name='age_type']" position="attributes">
                    <attribute name="required">0</attribute>
                    <attribute name="readonly">1</attribute>
                </xpath>
                <xpath expr="//field[@name='journal_id']" position="attributes">
                    <attribute name="domain">[('type', '=', 'general')]</attribute>
                    <attribute name="readonly">1</attribute>
                    <attribute name="force_save">1</attribute>
                </xpath>



            </field>
        </record>
    
    </data>
    

</odoo>
