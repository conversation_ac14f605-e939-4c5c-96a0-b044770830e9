from datetime import date
from odoo import api, fields, models, _
from odoo.tools import float_compare, float_is_zero
from odoo.exceptions import UserError, ValidationError

import calendar
from datetime import date, datetime
from dateutil.relativedelta import relativedelta


class AssetProgress(models.Model):
    _inherit = 'asset.progress'

    is_selected_all = fields.Boolean('Select All Lines', default=False)
    asset_group_id = fields.Many2one('account.asset.group', string='Asset Minor')
    stock_location_id = fields.Many2one('asset.location', string='Asset Location')
    phase_project_cip_id = fields.Many2one('phase.project.cip', string='Project')

    @api.onchange('model_id')
    def _onchange_model(self):
        self.journal_id = self.model_id.journal_id.id
    
    @api.onchange('phase_project_cip_id')
    def _onchange_phase_project_cip_id(self):
        if self.phase_project_cip_id:
            list_cip = self.env['account.asset.cip'].search([
                ('phase_project_cip_id', '=', self.phase_project_cip_id.id),
                ('is_asset', '=', False),
                ('cip_status', 'in', ['draft', False]),
            ])
            lines = [(5,0,0)]
            for cip in list_cip:
                data = {
                    'selected': False,
                    'cip_id': cip.id,
                }
                lines.append((0, 0, data))
            self.line_ids = lines
        else:
            list_cip = self.env['account.asset.cip'].search([
                ('is_asset', '=', False),
                ('cip_status', 'in', ['draft', False]),
            ])
            lines = [(5,0,0)]
            for cip in list_cip:
                data = {
                    'selected': False,
                    'cip_id': cip.id,
                }
                lines.append((0, 0, data))
            self.line_ids = lines


    @api.depends('line_ids', 'line_ids.selected', 'line_ids.original_value', 'is_selected_all')
    def _compute_total(self):
        for rec in self:
            rec.amount_total = sum(x.original_value for x in rec.line_ids.filtered(lambda x: x.selected))

    @api.onchange('is_selected_all')
    def _onchange_is_selected_all(self):
        if self.is_selected_all:
            for line in self.line_ids:
                line.selected = True
        else:
            for line in self.line_ids:
                line.selected = False

    @api.model
    def default_get(self, fields):
        res = super(AssetProgress, self).default_get(fields)
        list_cip = self.env['account.asset.cip'].search([
            ('is_asset', '=', False),
            ('cip_status', 'in', ['draft', False]),
        ])
        lines = []
        for cip in list_cip:
            data = {
                'selected': False,
                'cip_id': cip.id,
            }
            lines.append((0, 0, data))
        res['line_ids'] = lines
        return res
        

    # @api.onchange('asset_cost_progress_id', 'model_id')
    # def onchange_asset_cost_progress(self):
    #     if self.asset_cost_progress_id and self.model_id:
    #         list_cip = self.env['account.asset.cip'].search([('cip_id', '=', self.asset_cost_progress_id.id), ('is_asset', '=', False), ('model_id', '=', self.model_id.id)])
    #         lines = []
    #         for cip in list_cip:
    #             data = {
    #                 'selected': False,
    #                 'cip_id': cip.id,
    #             }
    #             lines.append((0, 0, data))
    #         self.line_ids = lines

    def button_done(self):
        """ function to generate asset and journal items """
        for rec in self:
            lines = [x for x in rec.line_ids if x.selected]
            
            # Validation: Check if one phase_project_cip_id has only one model_id
            phase_projects = {}
            for line in lines:
                if line.cip_id.phase_project_cip_id:
                    phase_id = line.cip_id.phase_project_cip_id.id
                    model_id = line.cip_id.model_id.id
                    if phase_id in phase_projects:
                        if phase_projects[phase_id] != model_id:
                            raise UserError(_('Phase Project CIP "%s" cannot have different Asset Major. Please ensure one Phase Project CIP has only one Asset Major.') % line.cip_id.phase_project_cip_id.name)
                    else:
                        phase_projects[phase_id] = model_id
            
            # Group lines by phase_project_cip_id
            project_groups = {}
            for line in lines:
                project_key = line.cip_id.phase_project_cip_id.id if line.cip_id.phase_project_cip_id else 'no_project'
                if project_key not in project_groups:
                    project_groups[project_key] = []
                project_groups[project_key].append(line)
            
            # Create separate assets for each project
            created_assets = []
            for project_key, project_lines in project_groups.items():
                # Update CIP status for lines in this project
                for line in project_lines:
                    line.cip_id.write({
                        'is_asset': True,
                        'cip_status': 'capitalized',
                    })
                
                # Get project info
                project_name = ""
                if project_key != 'no_project':
                    project_obj = self.env['phase.project.cip'].browse(project_key)
                    project_name = f" - {project_obj.name}"
                
                # Calculate total amount for this project
                project_amount = sum(x.original_value for x in project_lines)
                
                # Get model_id from first line (already validated to be same within project)
                model_id = project_lines[0].cip_id.model_id
                cip_id = project_lines[0].cip_id
                
                # Create asset for this project
                vals = {
                    'name': f"{rec.name}{project_name}",
                    'qty': 1,
                    'model_id': model_id.id,
                    'asset_group_id': cip_id.asset_group_id.id,
                    'stock_location_id': cip_id.stock_location_id.id,
                    'acquisition_date': cip_id.date_acquisition,
                    'method': cip_id.method,
                    'method_number': cip_id.method_number,
                    'method_period': cip_id.method_period,
                    'account_asset_id': cip_id.account_asset_id.id,
                    'account_depreciation_id': cip_id.account_depreciation_id.id,
                    'account_depreciation_expense_id': cip_id.account_depreciation_expense_id.id,
                    'journal_id': cip_id.journal_id.id,
                    'date_received': rec.date,
                    'original_value': project_amount,
                    'asset_type': 'purchase',
                    'state': 'draft',
                    
                    # 'origin_ids': [(4, x.move_line_id.id) for x in project_lines],
                }
                new_asset = self.env['account.asset'].new(vals)
                new_asset._onchange_model_id()  # to assign journal and accounts
                vals = new_asset._convert_to_write(new_asset._cache)
                asset = self.env['account.asset'].create(vals)
                created_assets.append(asset)
                
                # Prepare journal data for this project
                if not model_id:
                    raise UserError(_('Model ID is required for CIP lines'))
                if not model_id.journal_id:
                    raise UserError(_('Journal is not configured for Asset Major: %s') % model_id.name)
                if not model_id.cip_account_id:
                    raise UserError(_('CIP Account is not configured for Asset Major: %s') % model_id.name)
                if not model_id.account_asset_id:
                    raise UserError(_('Asset Account is not configured for Asset Major: %s') % model_id.name)
                
                journal_id = model_id.journal_id.id
                credit_account_id = model_id.cip_account_id.id
                debit_account_id = model_id.account_asset_id.id
                
                journals_data = [{
                    'journal_id': journal_id,
                    'credit_account_id': credit_account_id,
                    'debit_account_id': debit_account_id,
                    'amount': project_amount,
                    'phase_lines': project_lines,
                    'phase_id': project_key
                }]
                
                # Create journal entries for this asset
                context = {
                    'name': f"{rec.name}{project_name}",
                    'journals_data': journals_data,
                }
                asset.with_context(context).action_move_create_multi_credit()
            
            rec.write({'state': 'done'})
        return True


class AssetProgressLine(models.Model):
    _inherit = 'asset.progress.line'

    cip_id = fields.Many2one('account.asset.cip', 'CIP Name')
    parent_id = fields.Many2one('account.asset', 'Parent', related='cip_id.parent_id', store=True)
    acquisition_date = fields.Date('Acquisition Date', related='cip_id.acquisition_date', store=True)
    original_value = fields.Monetary('Original Value', related='cip_id.original_value', store=True)
    method = fields.Selection(
        selection=[
            ('linear', 'Straight Line'),
            ('degressive', 'Declining'),
            ('degressive_then_linear', 'Declining then Straight Line')
        ],
        string='Method', related='cip_id.method', store=True)
    book_value = fields.Monetary('Book Value', related='cip_id.book_value', store=True)
    value_residual = fields.Monetary('Depreciable Value', related='cip_id.value_residual', store=True)
    account_asset_id = fields.Many2one('account.account', 'Fixed Asset Account', related='cip_id.account_asset_id', store=True)
    account_depreciation_id = fields.Many2one('account.account', 'Depreciation Account', related='cip_id.account_depreciation_id', store=True)
    account_depreciation_expense_id = fields.Many2one('account.account', 'Expense Account', related='cip_id.account_depreciation_expense_id', store=True)
    currency_id = fields.Many2one('res.currency', 'Currency', related='cip_id.currency_id', store=True)

