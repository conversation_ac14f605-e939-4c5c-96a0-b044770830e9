<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="view_asset_progress_form_inherit" model="ir.ui.view">
            <field name="name">asset.progress.form.inherit</field>
            <field name="model">asset.progress</field>
            <field name="inherit_id" ref="account_asset_extension.view_asset_progress_form" />
            <field name="arch" type="xml">
                <!-- <xpath expr="//field[@name='asset_cost_progress_id']" position="before">
                    <field name="phase_project_cip_id" string="Project"/>
                </xpath> -->

                <xpath expr="//field[@name='date']" position="after">
                    <field name="phase_project_cip_id" string="Project"/>
                </xpath>
                <xpath expr="//field[@name='asset_cost_progress_id']" position="attributes">
                    <attribute name="required">0</attribute>
                    <attribute name="invisible">1</attribute>
                </xpath>

                <xpath expr="//field[@name='model_id']" position="replace"/>
                
                <!-- <xpath expr="//field[@name='model_id']" position="attributes">
                    <attribute name="string">Asset Major</attribute>
                    <attribute name="required">0</attribute>
                </xpath> -->
                <!-- <xpath expr="//field[@name='model_id']" position="after">
                    <field name="asset_group_id" readonly="state == 'done'" domain="[('asset_major_id','=', model_id)]"/>
                </xpath> -->
                <xpath expr="//field[@name='date_acquisition']" position="before">
                    <field name="stock_location_id" readonly="state == 'done'"/>
                </xpath>
                <xpath expr="//field[@name='qty']" position="after">
                    <field name="is_selected_all"/>
                </xpath>
                <xpath expr="//field[@name='account_id']" position="attributes">
                    <attribute name="required">0</attribute>
                    <attribute name="invisible">1</attribute>
                </xpath>
                <xpath expr="//field[@name='qty']" position="attributes">
                    <attribute name="required">0</attribute>
                    <attribute name="invisible">1</attribute>
                </xpath>

                <xpath expr="//field[@name='journal_id']" position="attributes">
                    <!-- <attribute name="domain">[('type', '=', 'general')]</attribute> -->
                    <attribute name="invisible">1</attribute>
                </xpath>
                <xpath expr="//list" position="replace">
                    <list editable="bottom">
                        <field name="selected"/>
                        <field name="cip_id" domain="[('phase_project_cip_id', '=', parent.phase_project_cip_id)]"/>
                        <field name="parent_id" optional="hide"/>
                        <field name="acquisition_date"/>
                        <field name="original_value"/>
                        <field name="method"/>
                        <field name="book_value"/>
                        <field name="value_residual" widget="monetary"/>
                        <field name="account_asset_id" optional="show"/>
                        <field name="account_depreciation_id" optional="show"/>
                        <field name="account_depreciation_expense_id" optional="show"/>
                        <field name="currency_id" groups="base.group_multi_currency" optional="show"/>
<!--                        <field name="asset_properties"/>-->
<!--                        <field name="state"/>-->
<!--                        <field name="activity_exception_decoration" widget="activity_exception"/>-->
<!--                        <field name="activity_ids" widget="list_activity" optional="hide"/>-->
                    </list>
                </xpath>
            </field>
        </record>
    </data>
</odoo>
