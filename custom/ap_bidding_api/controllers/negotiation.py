from odoo import SUPERUSER_ID, api, fields, models, _
from odoo import http
from odoo.http import request
from odoo.exceptions import AccessDenied
import logging, binascii, os, json
from datetime import datetime, timedelta
import pytz
from werkzeug import Response

_logger = logging.getLogger(__name__)

class NegotiationAPIController(http.Controller):

    # all negotiation
    @http.route('/api/negotiation', type='json', auth='public', methods=['GET'], csrf=False)
    def send_negotiation_data(self, **kwargs):
        headers = request.httprequest.headers
        token = str(headers.get('token', {}))
        user_id = request.env['res.users'].sudo().search([('token', '=', token)], limit=1)

        if not user_id:
            return {'status': 400, 'message': 'Invalid Token.'}

        # Timezone dan expired token check
        tz_name = user_id.tz or 'UTC'
        tz = pytz.timezone(tz_name)
        now_user_tz = datetime.now(tz)
        now_user_tz = now_user_tz.strftime('%Y-%m-%d %H:%M:%S')

        if user_id.expired_token_date:
            expired_token_utc = user_id.expired_token_date.replace(tzinfo=pytz.utc)
            expired_token_tz = expired_token_utc.astimezone(tz)
            expired_token_tz = expired_token_tz.strftime('%Y-%m-%d %H:%M:%S')

            if expired_token_tz < now_user_tz:
                return {'status': 401, 'message': 'Token expired.'}

        # Ambil semua negotiation yang aktif
        negotiations = request.env['bidding.negotiation'].sudo().search([('active', '=', True)])
        if not negotiations:
            return {'status': 404, 'message': 'No negotiation data found.'}

        result = []
        for nego in negotiations:
            negotiation_data = {
                'id': nego.id,
                'name': nego.name,
                'bidding_id': nego.bidding_id.id if nego.bidding_id else None,
                'bidding_description': nego.bidding_description,
                'bidding_type': nego.bidding_type,
                'partner_id': nego.partner_id.id if nego.partner_id else None,
                'partner_reference': nego.partner_reference,
                'buyer_id': nego.buyer_id.id if nego.buyer_id else None,
                'buyer_name': nego.buyer_id.name if nego.buyer_id else '',
                'requestor_id': nego.requestor_id.id if nego.requestor_id else None,
                'submitted_date': fields.Date.to_string(nego.submitted_date),
                'vendor_status': nego.vendor_status,
                'due_diligence_status': nego.due_diligence_status,
                'unit_id': nego.unit_id.id if nego.unit_id else None,
                'group_id': nego.group_id.id if nego.group_id else None,
                'delivery_location_id': nego.delivery_location_id.id if nego.delivery_location_id else None,
                'delivery_term_id': nego.delivery_term_id.id if nego.delivery_term_id else None,
                'company_id': nego.company_id.id if nego.company_id else None,
                'state': nego.state,
                'notes': nego.notes,
                'amount_total': nego.amount_total,
                'attachments': [],
                'line_ids': [],
            }

            # Attachments
            for att in nego.attachment_ids:
                negotiation_data['attachments'].append({
                    'id': att.id,
                    'display_name': att.display_name,
                    'datas': att.datas,
                    'store_fname': att.store_fname,
                    'mimetype': att.mimetype,
                })

            # Product Lines
            for line in nego.line_ids:
                negotiation_data['line_ids'].append({
                    'id': line.id,
                    'product_tmpl_id': line.product_tmpl_id.id if line.product_tmpl_id else None,
                    'product_tmpl_name': line.product_tmpl_id.name if line.product_tmpl_id else '',
                    'product_variant_id': line.product_variant_id.id if line.product_variant_id else None,
                    'propose_variant': line.propose_variant,
                    'description': line.description,
                    'quantity': line.quantity,
                    'product_uom_id': line.product_uom_id.id if line.product_uom_id else None,
                    'product_uom_name': line.product_uom_id.name if line.product_uom_id else '',
                    'is_percentage': line.is_percentage,
                    'currency_id': line.currency_id.id if line.currency_id else None,
                    'currency_name': line.currency_id.name if line.currency_id else '',
                    'unit_price': line.unit_price,
                    'exchange_rate': line.exchange_rate,
                    'is_manajemen_fee': line.is_manajemen_fee,
                    'total': line.total,
                    'is_award_line': line.is_award_line,
                })

            result.append(negotiation_data)

        return {
            'status': 200,
            'message': 'Success',
            'data': result
        }

    # berdasarkan id negotiation
    @http.route('/api/negotiation/<int:negotiation_id>', type='json', auth='public', methods=['GET'], csrf=False)
    def get_negotiation_by_id(self, negotiation_id, **kwargs):
        headers = request.httprequest.headers
        token = str(headers.get('token', {}))
        user_id = request.env['res.users'].sudo().search([('token', '=', token)], limit=1)

        if not user_id:
            return {'status': 400, 'message': 'Invalid Token.'}

        tz_name = user_id.tz or 'UTC'
        tz = pytz.timezone(tz_name)
        now_user_tz = datetime.now(tz)
        now_user_tz = now_user_tz.strftime('%Y-%m-%d %H:%M:%S')

        if user_id.expired_token_date:
            expired_token_utc = user_id.expired_token_date.replace(tzinfo=pytz.utc)
            expired_token_tz = expired_token_utc.astimezone(tz)
            expired_token_tz = expired_token_tz.strftime('%Y-%m-%d %H:%M:%S')

            if str(expired_token_tz) < str(now_user_tz):
                return {'status': 401, 'message': 'Token expired.'}

        # Cari data negotiation berdasarkan ID
        nego = request.env['bidding.negotiation'].sudo().search([('id', '=', negotiation_id), ('active', '=', True)], limit=1)
        
        if not nego:
            return {'status': 404, 'message': 'Negotiation not found.'}

        result = []
        negotiation_data = {
            'id': nego.id,
            'name': nego.name,
            'bidding_id': nego.bidding_id.id if nego.bidding_id else None,
            'bidding_description': nego.bidding_description,
            'bidding_type': nego.bidding_type,
            'partner_id': nego.partner_id.id if nego.partner_id else None,
            'partner_reference': nego.partner_reference,
            'buyer_id': nego.buyer_id.id if nego.buyer_id else None,
            'buyer_name': nego.buyer_id.name if nego.buyer_id else '',
            'requestor_id': nego.requestor_id.id if nego.requestor_id else None,
            'submitted_date': fields.Date.to_string(nego.submitted_date),
            'vendor_status': nego.vendor_status,
            'due_diligence_status': nego.due_diligence_status,
            'unit_id': nego.unit_id.id if nego.unit_id else None,
            'group_id': nego.group_id.id if nego.group_id else None,
            'delivery_location_id': nego.delivery_location_id.id if nego.delivery_location_id else None,
            'delivery_term_id': nego.delivery_term_id.id if nego.delivery_term_id else None,
            'company_id': nego.company_id.id if nego.company_id else None,
            'state': nego.state,
            'notes': nego.notes,
            'amount_total': nego.amount_total,
            'attachments': [],
            'line_ids': [],
        }

        # Attachments
        for att in nego.attachment_ids:
            negotiation_data['attachments'].append({
                'id': att.id,
                'display_name': att.display_name,
                'datas': att.datas,
                'store_fname': att.store_fname,
                'mimetype': att.mimetype,
            })

        # Product Lines
        for line in nego.line_ids:
            negotiation_data['line_ids'].append({
                'id': line.id,
                'product_tmpl_id': line.product_tmpl_id.id if line.product_tmpl_id else None,
                'product_tmpl_name': line.product_tmpl_id.name if line.product_tmpl_id else '',
                'product_variant_id': line.product_variant_id.id if line.product_variant_id else None,
                'propose_variant': line.propose_variant,
                'description': line.description,
                'quantity': line.quantity,
                'product_uom_id': line.product_uom_id.id if line.product_uom_id else None,
                'product_uom_name': line.product_uom_id.name if line.product_uom_id else '',
                'is_percentage': line.is_percentage,
                'currency_id': line.currency_id.id if line.currency_id else None,
                'currency_name': line.currency_id.name if line.currency_id else '',
                'unit_price': line.unit_price,
                'exchange_rate': line.exchange_rate,
                'is_manajemen_fee': line.is_manajemen_fee,
                'total': line.total,
                'is_award_line': line.is_award_line,
            })

        result.append(negotiation_data)

        return {
            'status': 200,
            'message': 'Success',
            'data': result
        }

    # list negotiation berdasarkan bidding id
    @http.route('/api/negotiation/by-bidding/<int:bidding_id>', type='http', auth='public', methods=['GET'], csrf=False)
    def get_negotiations_by_bidding_id(self, bidding_id):
        headers = request.httprequest.headers
        token = str(headers.get('token', {}))
        user_id = request.env['res.users'].sudo().search([('token', '=', token)], limit=1)

        if not user_id:
            data = {'status': 400, 'message': 'Invalid Token.'}
            headers = {'Content-Type': 'application/json'}
            return Response(json.dumps(data), headers=headers, status=400)

        # Timezone dan expired token check
        tz_name = user_id.tz or 'UTC'
        tz = pytz.timezone(tz_name)
        now_user_tz = datetime.now(tz)
        now_user_tz = now_user_tz.strftime('%Y-%m-%d %H:%M:%S')

        if user_id.expired_token_date:
            expired_token_utc = user_id.expired_token_date.replace(tzinfo=pytz.utc)
            expired_token_tz = expired_token_utc.astimezone(tz)
            expired_token_tz = expired_token_tz.strftime('%Y-%m-%d %H:%M:%S')

            if expired_token_tz < now_user_tz:
                data = {'status': 401, 'message': 'Token expired.'}
                headers = {'Content-Type': 'application/json'}
                return Response(json.dumps(data), headers=headers, status=401)

        # Cari negotiation berdasarkan bidding_id
        negotiations = request.env['bidding.negotiation'].sudo().search([
            ('bidding_id', '=', bidding_id),
            ('active', '=', True)
        ])

        if not negotiations:
            data = {'status': 404, 'message': 'No negotiations found for this bidding ID.'}
            headers = {'Content-Type': 'application/json'}
            return Response(json.dumps(data), headers=headers, status=404)

        import re
        last_name = None
        for nego in negotiations:
            name = str(nego.name)
            match = re.search(r'(.*?)(\s*-\s*)(\d+)$', name)
            if match:
                prefix, sep, num = match.groups()
                new_num = str(int(num) + 1).zfill(len(num))
                new_name = f"{prefix}{sep}{new_num}"
                last_name = new_name
            elif name.isdigit():
                new_name = str(int(name) + 1).zfill(len(name))
                last_name = new_name
        # Ambil id bidding dari salah satu negotiation (jika ada)
        bidding_id_val = negotiations[0].bidding_id.id if negotiations else None
        data = {
            'status': 200,
            'message': 'Success',
            'data': {
                'bidding_id': bidding_id_val,
                'name': last_name
            } if last_name else {}
        }
        headers = {'Content-Type': 'application/json'}
        return Response(json.dumps(data, indent=4, sort_keys=True, default=str), headers=headers)

    @http.route('/api/negotiation/create', type='json', auth='public', methods=['POST'], csrf=False)
    def create_negotiation(self):
        try:
            args = request.httprequest.get_json(force=True)
            headers = request.httprequest.headers
            token = str(headers.get('token', ''))

            user_id = request.env['res.users'].sudo().search([('token', '=', token)], limit=1)
            if not user_id:
                return {'status': 400, 'message': 'Invalid Token.'}

            tz_name = user_id.tz or 'UTC'
            tz = pytz.timezone(tz_name)
            now_user_tz = datetime.now(tz).strftime('%Y-%m-%d %H:%M:%S')

            if user_id.expired_token_date:
                expired_token_tz = user_id.expired_token_date.replace(tzinfo=pytz.utc).astimezone(tz).strftime('%Y-%m-%d %H:%M:%S')
                if expired_token_tz < now_user_tz:
                    return {'status': 401, 'message': 'Token expired.'}

            negotiation = args.get('negotiation', {})
            line_ids = args.get('line_ids', [])
            attachments = args.get('attachments', [])

            attachment_list = []
            for a in attachments:
                attachment_vals = {
                    'name': a.get('display_name'),
                    'datas': a.get('datas'),
                    'mimetype': a.get('mimetype'),
                }
                attachment = request.env['ir.attachment'].sudo().create(attachment_vals)
                attachment_list.append((4, attachment.id))

            line_list = []
            for l in line_ids:
                line_list.append((0, 0, {
                    'product_tmpl_id': l.get('product_tmpl_id'),
                    'product_variant_id': l.get('product_variant_id'),
                    'propose_variant': l.get('propose_variant'),
                    'description': l.get('description'),
                    'quantity': l.get('quantity'),
                    'product_uom_id': l.get('product_uom_id'),
                    'is_percentage': l.get('is_percentage'),
                    'currency_id': l.get('currency_id'),
                    'unit_price': l.get('unit_price'),
                    'exchange_rate': l.get('exchange_rate'),
                    'is_manajemen_fee': l.get('is_manajemen_fee'),
                    'total': l.get('total'),
                    'is_award_line': l.get('is_award_line'),
                }))

            negotiation_vals = {
                'name': negotiation.get('name'),
                'bidding_id': negotiation.get('bidding_id'),
                'bidding_description': negotiation.get('bidding_description'),
                'bidding_type': negotiation.get('bidding_type'),
                'partner_id': negotiation.get('partner_id'),
                'partner_reference': negotiation.get('partner_reference'),
                'buyer_id': negotiation.get('buyer_id'),
                'requestor_id': negotiation.get('requestor_id'),
                'submitted_date': negotiation.get('submitted_date'),
                'vendor_status': negotiation.get('vendor_status'),
                'due_diligence_status': negotiation.get('due_diligence_status'),
                'unit_id': negotiation.get('unit_id'),
                'group_id': negotiation.get('group_id'),
                'delivery_location_id': negotiation.get('delivery_location_id'),
                'delivery_term_id': negotiation.get('delivery_term_id'),
                'payment_term_id': negotiation.get('payment_term_id'),
                'company_id': negotiation.get('company_id'),
                'state': negotiation.get('state'),
                'notes': negotiation.get('notes'),
                'amount_total': negotiation.get('amount_total'),
                'line_ids': line_list,
                'attachment_ids': attachment_list,
            }

            negotiation_record = request.env['bidding.negotiation'].sudo().create(negotiation_vals)

            return {
                'status': 201,
                'message': 'Negotiation created successfully.',
                'negotiation_id': negotiation_record.id
            }

        except Exception as e:
            return {
                'status': 500,
                'message': 'Failed to create negotiation.',
                'error': str(e)
            }

    @http.route('/api/negotiation/update', type='json', auth='public', methods=['POST'], csrf=False)
    def update_negotiation(self):
        try:
            args = request.httprequest.get_json(force=True)
            headers = request.httprequest.headers
            token = str(headers.get('token', ''))

            user_id = request.env['res.users'].sudo().search([('token', '=', token)], limit=1)
            if not user_id:
                return {'status': 400, 'message': 'Invalid Token.'}

            # Token expiration check
            tz_name = user_id.tz or 'UTC'
            tz = pytz.timezone(tz_name)
            now_user_tz = datetime.now(tz).strftime('%Y-%m-%d %H:%M:%S')

            if user_id.expired_token_date:
                expired_token_tz = user_id.expired_token_date.replace(tzinfo=pytz.utc).astimezone(tz).strftime('%Y-%m-%d %H:%M:%S')
                if expired_token_tz < now_user_tz:
                    return {'status': 401, 'message': 'Token expired.'}

            negotiation_id = args.get('negotiation_id')
            if not negotiation_id:
                return {'status': 400, 'message': 'Negotiation ID is required.'}

            negotiation_record = request.env['bidding.negotiation'].sudo().browse(negotiation_id)
            if not negotiation_record.exists():
                return {'status': 404, 'message': 'Negotiation not found.'}

            negotiation_data = args.get('negotiation', {})
            line_ids = args.get('line_ids', [])
            attachments = args.get('attachments', [])

            # Clear existing lines
            negotiation_record.write({'line_ids': [(5, 0, 0)]})

            # Create new lines
            line_list = []
            for l in line_ids:
                line_list.append((0, 0, {
                    'product_tmpl_id': l.get('product_tmpl_id'),
                    'product_variant_id': l.get('product_variant_id'),
                    'propose_variant': l.get('propose_variant'),
                    'description': l.get('description'),
                    'quantity': l.get('quantity'),
                    'product_uom_id': l.get('product_uom_id'),
                    'is_percentage': l.get('is_percentage'),
                    'currency_id': l.get('currency_id'),
                    'unit_price': l.get('unit_price'),
                    'exchange_rate': l.get('exchange_rate'),
                    'is_manajemen_fee': l.get('is_manajemen_fee'),
                    'total': l.get('total'),
                    'is_award_line': l.get('is_award_line'),
                }))

            # Update negotiation fields
            negotiation_record.write({
                'name': negotiation_data.get('name'),
                'bidding_id': negotiation_data.get('bidding_id'),
                'bidding_description': negotiation_data.get('bidding_description'),
                'bidding_type': negotiation_data.get('bidding_type'),
                'partner_id': negotiation_data.get('partner_id'),
                'partner_reference': negotiation_data.get('partner_reference'),
                'buyer_id': negotiation_data.get('buyer_id'),
                'requestor_id': negotiation_data.get('requestor_id'),
                'submitted_date': negotiation_data.get('submitted_date'),
                'vendor_status': negotiation_data.get('vendor_status'),
                'due_diligence_status': negotiation_data.get('due_diligence_status'),
                'unit_id': negotiation_data.get('unit_id'),
                'group_id': negotiation_data.get('group_id'),
                'delivery_location_id': negotiation_data.get('delivery_location_id'),
                'delivery_term_id': negotiation_data.get('delivery_term_id'),
                'company_id': negotiation_data.get('company_id'),
                'state': negotiation_data.get('state'),
                'notes': negotiation_data.get('notes'),
                'amount_total': negotiation_data.get('amount_total'),
                'line_ids': line_list,
            })

            # Create and append new attachments
            for a in attachments:
                attachment_vals = {
                    'name': a.get('display_name'),
                    'datas': a.get('datas'),
                    'mimetype': a.get('mimetype'),
                }
                attachment = request.env['ir.attachment'].sudo().create(attachment_vals)
                negotiation_record.write({'attachment_ids': [(4, attachment.id)]})

            return {
                'status': 200,
                'message': 'Negotiation updated successfully.',
                'negotiation_id': negotiation_record.id
            }

        except Exception as e:
            return {
                'status': 500,
                'message': 'Failed to update negotiation.',
                'error': str(e)
            }


