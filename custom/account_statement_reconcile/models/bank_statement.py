from odoo import _, api, fields, models
from odoo.exceptions import ValidationError
import logging
from odoo.osv.expression import AND, OR
from odoo.tools.date_utils import end_of

_logger = logging.getLogger(__name__)


class BankStatement(models.Model):
    _name = "bank.statement"
    _description = "Bank Statement"
    _inherit = ['mail.thread']

    name = fields.Char(string="Name", compute='_compute_name', store=True, copy=False, tracking=True)
    journal_id = fields.Many2one(string="Bank Account", comodel_name="account.journal", required=True, domain="[('type', '=', 'bank')]", tracking=True,copy=True)   
    account_number = fields.Char(string="Account Number", related="journal_id.bank_account_id.account_number", tracking=True)
    statement_id = fields.Char(string="Statement ID", tracking=True,copy=False)
    period_start = fields.Date(string="Period Start", required=True, tracking=True)
    period_end = fields.Date(string="Period End", required=True, tracking=True)
    opening_booked = fields.Monetary(string="Opening Booked", default=0, tracking=True)
    closing_booked = fields.Monetary(string="Closing Booked", compute='_compute_closing_booked', store=True)
    

    entry_type = fields.Selection([
        ("manual", "Manual"),
        ("mt940", "MT940"),
        ("import", "Import")], string="Entry Type", required=True, default="manual", tracking=True,copy=False
    )

    state = fields.Selection([
        ("incomplete", "Incomplete"),
        ("completed", "Completed")], string="Status", default="incomplete", tracking=True,copy=False
    )

    company_id = fields.Many2one(string="Company", comodel_name="res.company",
                                 related="journal_id.company_id", store=True)

    currency_id = fields.Many2one(string="Journal Currency", comodel_name="res.currency",
                                  compute="_compute_currency_id", store=True)
    
    incomplete_line_ids = fields.One2many(string='Incomplete statement lines', comodel_name='bank.statement.line', inverse_name='statement_id', domain=[('state', '=', 'incomplete')], tracking=True,copy=True)
    completed_line_ids = fields.One2many(string='Completed statement lines', comodel_name='bank.statement.line', inverse_name='statement_id', domain=[('state', '=', 'completed')], tracking=True)
    
    reconcile_rule_id = fields.Many2one(string="Reconciliation Rule", comodel_name="reconcile.matching.rule", compute="_compute_reconcile_rule_id", store=True, tracking=True)
    
    @api.depends("journal_id")
    def _compute_reconcile_rule_id(self):
        """Find matching reconciliation rule based on journal_id"""
        for rec in self:
            matching_rule = self.env['reconcile.matching.rule'].search([
                ('journal_ids', 'in', [rec.journal_id.id]),
                ('active', '=', True)
            ], limit=1)
            rec.reconcile_rule_id = matching_rule.id if matching_rule else False

    @api.depends("journal_id")
    def _compute_currency_id(self):
        for rec in self:
            rec.currency_id = rec.journal_id.currency_id or rec.company_id.currency_id
    
    @api.depends('create_date')
    def _compute_name(self):
        for rec in self:
            rec.name = _("%(journal_code)s Statement", journal_code=rec.journal_id.code)    
    
    @api.depends('opening_booked', 'incomplete_line_ids.debit', 'incomplete_line_ids.credit')
    def _compute_closing_booked(self):
        for rec in self:
            total_diff = sum(line.credit - line.debit for line in rec.incomplete_line_ids)
            rec.closing_booked = rec.opening_booked + total_diff            
    

    
    @api.constrains('period_start', 'period_end')
    def _check_period_dates(self):
        """Constraint to ensure period_end is not less than period_start"""
        for record in self:
            if record.period_start and record.period_end:
                if record.period_end < record.period_start:
                    raise ValidationError(_(
                        'Period end date (%s) cannot be earlier than period start date (%s).'
                    ) % (record.period_end, record.period_start))
    
    @api.model
    def create(self, vals):
        statement_id = vals.get('statement_id')
        statement_id = statement_id.strip() if isinstance(statement_id, str) else statement_id

        if not statement_id:
             vals['statement_id'] = self.env['ir.sequence'].next_by_code('bank_statement_id_sequence') or ("Sequence Not Found")
        return super(BankStatement, self).create(vals)

    def action_manual_reconcile(self):
        self.ensure_one()
        return {
            'name': 'Search Bank Statements',
            'type': 'ir.actions.act_window',
            'res_model': 'bank.statement.search.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_journal_id': self.journal_id.id,
                'default_period_start': self.period_start,
                'default_period_end': self.period_end,
                'default_statement_id': self.statement_id,
                'default_bank_statement_id': self.id,
                'default_reconcile_rule_id': self.reconcile_rule_id.id,
                'default_statement_line_ids': self.incomplete_line_ids.ids,
            }
        }
    def auto_reconcile(self):
        """
        Auto reconcile function to process incomplete statement lines.
        Uses the reconcile_rule_id to find and apply matching rules.
        """
        self.ensure_one()
        
        # First check if reconcile rule exists
        if not self.reconcile_rule_id:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _("No Matching Rule"),
                    'message': _("No reconciliation matching rule found for this bank account."),
                    'sticky': False,
                    'type': 'warning',
                }
            }
            
        # Then check if there are incomplete lines to process
        if not self.incomplete_line_ids:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _("Nothing to Reconcile"),
                    'message': _("There are no incomplete statement lines to reconcile."),
                    'sticky': False,
                    'type': 'warning',
                }
            }
        
        
        
        reconciled_count = 0
        for line in self.incomplete_line_ids:
            # Skip processing if line has already been reconciled
            if line.state == 'completed':
                continue
            
            # Find matching move lines
            matching_move_lines = self._find_matching_move_lines(line)
            
            if matching_move_lines:
                # Process reconciliation (includes criteria checking)
                tolerance = self._get_reconciliation_tolerance(line=matching_move_lines)
                if self._process_reconciliation(line, matching_move_lines, tolerance):
                    reconciled_count += 1
                else:
                    _logger.warning("Line number %s skipped: does not meet reconciliation criteria", line.line_number)

        # Mark statement as complete if all lines are reconciled
        if all(line.state == 'completed' for line in self.incomplete_line_ids):
            self.state = 'completed'
            
        # Return notification of result
        return self._prepare_reconciliation_result_message(reconciled_count)

    def _get_reconciliation_tolerance(self, line=None):
        """Get the global tolerance value for reconciliation"""
        param_name = 'account_statement_reconcile.general_amount_tolerance'
        param_value = self.env['ir.config_parameter'].sudo().get_param(param_name, '0.0')
        currency = self.env['ir.config_parameter'].sudo().get_param('account_statement_reconcile.currency_id', '0.0')
        currency_id = self.env['res.currency'].browse(int(currency)) if currency else self.env.company.currency_id
        conversion_amount = 0
        if line and line._name == 'bank.statement.line':
            conversion_amount = currency_id._convert(float(param_value), line.currency_id, self.env.company, line.transaction_date or fields.Date.today())
        elif line and line._name == 'account.move.line':
            conversion_amount = currency_id._convert(float(param_value), line.currency_id, self.env.company, line.date or fields.Date.today())
        try:
            return float(conversion_amount)
        except (ValueError, TypeError):
            _logger.warning("Invalid value for %s parameter: %s, using 0.0", param_name, param_value)
            return 0.0    
    
    def _find_matching_move_lines(self, line):
        """Find matching account.move.line entries based on criteria from the matching rule"""
        # Base domain for all searches - using list for better performance
        base_domain = [
            ('reconciled', '=', False),
            ('move_id.state', '=', 'posted'),
        ]
        # Add amount matching criteria if enabled in rules
        if self.reconcile_rule_id.match_amount:
            base_domain.extend(self._get_amount_criteria(line))

        matching_lines = self.env['account.move.line'].search(base_domain )
        if len(matching_lines) > 1:
            if self.reconcile_rule_id.match_date:
                matching_lines = matching_lines.filtered(lambda move_line: move_line.date == line.transaction_date)
                if len(matching_lines) == 1:
                    return matching_lines[0]

            if self.reconcile_rule_id.match_reference:
                matching_lines = matching_lines.filtered(lambda move_line: line.description in (move_line.name, move_line.ref))
                if len(matching_lines) == 1:
                    return matching_lines[0]
            
            if self.reconcile_rule_id.match_transaction_number:
                matching_lines = matching_lines.filtered(lambda move_line: line.description in (move_line.move_id.origin_payment_id.payment_plan_id.name))
                if len(matching_lines) == 1:
                    return matching_lines[0]
        
                


        
        # # Include date matching in SQL domain if enabled
        # if self.reconcile_rule_id.match_date:
        #     base_domain.append(('date', '=', line.transaction_date))
        
        
        # # Add reference matching criteria
        # if line.description:
        #     reference_criteria = self._get_reference_criteria(line)
        #     if reference_criteria:
        #         base_domain.extend(reference_criteria)
        
        # Add transaction type matching criteria if configured
        if self.reconcile_rule_id.transaction_mode and self.reconcile_rule_id.journal_transaction_ids:
            base_domain.append(('move_id.journal_id', 'in', self.reconcile_rule_id.journal_transaction_ids.ids))
        
        # Get settlement accounts for use in searches
        deposit_settlement_account = [
            self.env.company.account_credit_deposit_id.id, 
            self.env.company.account_debit_settlement_id.id
        ]
        
        # Two-step search strategy:
        # 1. First search for deposit_settlement lines (higher priority)
        deposit_domain = base_domain + [('deposit_settlement', '=', True),('account_id', 'not in', deposit_settlement_account)]
        matching_lines = self.env['account.move.line'].search(deposit_domain, limit=1)
        
        # 2. If no deposit_settlement lines found, search for regular reconcilable lines
        if not matching_lines:
            non_deposit_domain = base_domain + [
                ('deposit_settlement', '=', False),
                ('account_id.reconcile', '=', True)
            ]
            matching_lines = self.env['account.move.line'].search(non_deposit_domain, limit=1)
        
        # # Take first matching line if multiple found (avoiding Python filtering)
        # if matching_lines and len(matching_lines) > 1:
        #     matching_lines = matching_lines[0]
            
        # # Special processing for deposit_settlement lines
        # if matching_lines and matching_lines.deposit_settlement and matching_lines.move_id.deposit_settlement:
        #     if matching_lines.account_id.id in deposit_settlement_account:
        #         # Get the more relevant line from the same move that's not a settlement account
        #         related_lines = matching_lines.move_id.line_ids.filtered(
        #             lambda l: (l.deposit_settlement and 
        #                       l.account_id.id not in deposit_settlement_account and 
        #                       not l.reconciled)
        #         )
                
        #         if related_lines:
        #             matching_lines = related_lines
        #         else:
        #             # Fallback to find a suitable line if the related line isn't found
        #             fallback_domain = base_domain + [
        #                 ('id', '!=', matching_lines.id),
        #                 ('account_id', 'not in', deposit_settlement_account)
        #             ]
        #             matching_lines = self.env['account.move.line'].search(fallback_domain, limit=1)
        
        # Log results
        if matching_lines:
            _logger.info(
                "Found matching move line for bank statement line %s: Move: %s, Line: %s", 
                line.line_number, 
                matching_lines.move_id.name, 
                matching_lines.id
            )
        else:
            _logger.warning("No matching move lines found for bank statement line %s", line.line_number)
            
        return matching_lines
        
    def _get_amount_criteria(self, line):
        """Helper method to get amount matching criteria for search domain"""
        tolerance = self._get_reconciliation_tolerance(line=line)
        line._compute_balance()
        line_balance_abs = abs(line.balance)
        
        if line.balance < 0:  # Money coming in
            return [
                ('amount_currency', '>=', line_balance_abs - tolerance),
                ('amount_currency', '<=', line_balance_abs + tolerance)
            ]
        else:  # Money going out
            return [
                ('amount_currency', '>=', -line_balance_abs - tolerance),
                ('amount_currency', '<=', -line_balance_abs + tolerance)
            ]
            
    def _get_reference_criteria(self, line):
        """Helper method to get reference matching criteria for search domain"""
        rule = self.reconcile_rule_id
        criteria = []
        
        # Skip if no description in line
        if not line.description:
            return criteria
            
        # Case 1: Only match_reference is True
        if rule.match_reference and not rule.match_transaction_number:
            criteria = [
                '|',
                ('move_id.name', 'ilike', line.description),
                ('move_id.ref', 'ilike', line.description),
                
            ]
            
        # Case 2: Only match_transaction_number is True
        elif rule.match_transaction_number and not rule.match_reference:
            criteria = [
                ('move_id.origin_payment_id.payment_plan_id.name', 'ilike', line.description),
                
            ]
                
        # Case 3: Both match_reference and match_transaction_number are True
        elif rule.match_reference and rule.match_transaction_number:
            # Create OR criteria for domain
            criteria = [
                '|',
                 ('move_id.ref', 'ilike', line.description),
                 ('move_id.origin_payment_id.payment_plan_id.name', 'ilike', line.description),
            ]
            
        return criteria    # Helper methods for constructing search domains are now defined above
    # with the _find_matching_move_lines method

    def _process_reconciliation(self, line, matching_move_lines, tolerance=None):
        """Process reconciliation for a statement line with matching move lines"""
        # Use provided tolerance or get it if not provided
        if tolerance is None:
            tolerance = self._get_reconciliation_tolerance(line=matching_move_lines)
            
        # Analyze matching lines to determine amounts and line selection
        matching_data = self._analyze_matching_lines(line, matching_move_lines, tolerance)
        
        # Check if the line meets criteria for reconciliation
        if not matching_data.get('meets_criteria', False):
            _logger.warning("Bank statement line number %s does not meet reconciliation criteria (difference exceeds tolerance)", line.line_number)
            return False
          # Use the result_matching_lines from the analysis
        result_matching_lines = matching_data.get('result_matching_lines', matching_move_lines)
        
        # Check if the matching lines have deposit_settlement flag
        if result_matching_lines and result_matching_lines[0].deposit_settlement or result_matching_lines.move_id.deposit_settlement:
            # For deposit_settlement lines, do direct reconciliation without creating a new move
            
            success = self._process_deposit_settlement_reconciliation(line, result_matching_lines)

            if success:
                # Mark the bank statement line as completed
                move_name = result_matching_lines.move_id.name
                line.write({
                    'state': 'completed',
                    'reconcile_ref': move_name,
                    'move_id': result_matching_lines.move_id.id
                })
                _logger.info("Directly reconciled bank statement line %s with deposit_settlement line", line.id)
                return True
            return False
        
        # Standard flow for non-deposit_settlement lines
        move_vals = self._prepare_reconciliation_move(line, result_matching_lines, matching_data)
        
        # Create and post the move
        move = self.env['account.move'].create(move_vals)
        move.action_post()
        
        # Perform reconciliation
        self._reconcile_statement_with_moves(line, result_matching_lines, move)
        for matching_line in result_matching_lines:
            if matching_line.payment_id:
                matching_line.payment_id.action_reconcile()
        
        # Mark the bank statement line as completed and store move reference
        line.write({
            'state': 'completed',
            'move_id': move.id,
            'reconcile_ref': move.name  # Set reconcile_ref to the journal entry name
        })
        _logger.info("Reconciled bank statement line %s with move %s", line.id, move.id)
        return True
    

    def _analyze_matching_lines(self, line, matching_move_lines, tolerance=None):
        """Analyze matching lines to determine amounts and if we should use only first match"""
        # Use provided tolerance or get it if not provided
        if tolerance is None:
            tolerance = self._get_reconciliation_tolerance(line=matching_move_lines)
        
        # Handle empty matching_move_lines
        if not matching_move_lines:
            return {
                # 'use_only_first': False,
                'matching_amount': 0,
                'diff_amount': abs(line.balance or 0.0),
                'meets_criteria': False
            }
        diff_amount_base = tolerance
        # Normalize matching_move_lines to handle both recordsets and lists
        first_move_line = matching_move_lines
        line_balance_abs = abs( line.balance or 0.0)
            
        # Adjust tolerance for currency differences if needed
        exchange_rate = 1/matching_move_lines.currency_rate
        

        # Calculate matching amount and determine which lines to use
        # matching_amount, result_matching_lines = self._calculate_matching_amount(
        #     line, matching_move_lines, first_move_line, tolerance)
        
        # Calculate difference amount based on direction
        if line.balance < 0:  # Money coming in
            diff_amount = line_balance_abs - matching_move_lines.amount_currency
        else:  # Money going out
            diff_amount = line_balance_abs - abs(matching_move_lines.amount_currency)
        amount_currency_diff = diff_amount * exchange_rate
        
        # Check if difference exceeds tolerance
        meets_criteria = abs(diff_amount) <= tolerance
        
        return {
            # 'use_only_first': result_matching_lines is first_move_line,
            # 'matching_amount': matching_amount,
            'diff_amount': diff_amount,
            'diff_amount_base': diff_amount_base,
            'amount_currency_diff':amount_currency_diff,
            'exchange_rate': exchange_rate,
            'result_matching_lines': matching_move_lines,
            'meets_criteria': meets_criteria
        }
    
    
    def _adjust_tolerance_for_currency(self, move_line, base_tolerance):
        """
        Adjust tolerance based on currency exchange rate if needed.
        When currencies differ, the tolerance should be adjusted by the exchange rate:
        - exchange_rate = abs(balance / amount_currency)
        - adjusted_tolerance = base_tolerance * exchange_rate
        """
        if not move_line:
            return base_tolerance, 0

        # Check if we have the necessary currency fields and they are different
        has_different_currencies = (
            move_line.currency_id and 
            move_line.company_currency_id and
            move_line.currency_id and 
            move_line.company_currency_id and
            move_line.currency_id != move_line.company_currency_id
        )
        
        if has_different_currencies:
            # Get amount_currency safely, avoiding division by zero
            amount_currency = move_line.amount_currency 
            if not amount_currency or abs(amount_currency) < 0.000001:  # Avoid division by very small numbers
                return base_tolerance, 0
                
            # Calculate exchange rate from the move line's values
            balance = move_line.balance
            exchange_rate = abs(balance / amount_currency)
            
            # Apply exchange rate to tolerance
            adjusted_tolerance = base_tolerance * exchange_rate
            _logger.info(
                "Adjusted tolerance from %s to %s using exchange rate %s for move line %s", 
                base_tolerance, adjusted_tolerance, exchange_rate, move_line.id
            )
            return adjusted_tolerance, exchange_rate

        return base_tolerance, 0
    
    def _calculate_matching_amount(self, line, matching_move_lines, first_move_line, tolerance):
        """Calculate the matching amount and determine which lines to use"""
        if not first_move_line:
            return 0, matching_move_lines
            
        first_move_amount = first_move_line.balance or 0.0
        line_balance_abs = abs(line.balance)
        
        # Check if first line is a close enough match
        is_close_match = False
        if line.balance < 0:  # Money coming in
            is_close_match = abs(first_move_amount - line_balance_abs) <= tolerance
        else:  # Money going out
            is_close_match = abs(first_move_amount + line_balance_abs) <= tolerance
        
        if is_close_match:
            return first_move_amount, first_move_line
        
        # Calculate total for all lines if first line doesn't match closely
        if isinstance(matching_move_lines, list):
            total = sum(ml.balance for ml in matching_move_lines)
        elif hasattr(matching_move_lines, 'mapped'):
            total = sum(matching_move_lines.mapped('balance'))
        else:
            total = matching_move_lines.balance

        return total, matching_move_lines
        
    def _prepare_reconciliation_move(self, line, matching_move_lines, matching_data):
        """Prepare move values for reconciliation"""
        # Use reconcile_journal_id from company settings if available, otherwise use bank statement journal
        journal_id = self.env.company.reconcile_journal_id and self.env.company.reconcile_journal_id.id or self.journal_id.id
        
        move_vals = {
            'date': line.transaction_date,
            'journal_id': journal_id,
            'ref': f"Auto-reconciliation: {line.reconcile_ref or ''} - {line.transaction_date}",
            'move_type': 'entry',
            'line_ids': [],
        }
        
        # Add counterpart to bank account
        partner_id = matching_move_lines.partner_id.id 
        balance = abs(line.balance) * matching_data.get('exchange_rate', 1.0)
        move_vals['line_ids'].append((0, 0, {
            'account_id': self.journal_id.default_account_id.id,
            'partner_id': partner_id,
            'name': f"Bank Statement Line: {line.transaction_date}",
            'currency_id': line.currency_id.id,
            'debit': abs(balance) if line.balance < 0 else 0.0,
            'credit': abs(balance) if line.balance > 0 else 0.0,
            'amount_currency': abs(line.balance) if line.balance < 0 else -abs(line.balance),
        }))
        
        # Add line for each matched account.move.line
        
        self._add_single_matching_line(move_vals, matching_move_lines)
        
        # Add difference line if needed
        self._add_difference_line_to_move(move_vals, matching_data, line)
                
        return move_vals
    
    
    def _add_single_matching_line(self, move_vals, move_line):
        """Add a single matching line to move values"""
        if not move_line:
            return
            
        account_id = move_line.account_id.id if move_line.account_id else False
        partner_id = move_line.partner_id.id if move_line.partner_id else False
        name = move_line.move_id.name
        
        move_vals['line_ids'].append((0, 0, {
            'account_id': account_id,
            'partner_id': partner_id,
            'name': f"Match: {name}",
            'debit': move_line.credit,
            'credit': move_line.debit,
            'amount_currency': abs(move_line.amount_currency) if move_line.amount_currency < 0 else -abs(move_line.amount_currency),
            'currency_id': move_line.currency_id.id if move_line.currency_id else False,
        }))
    def _add_difference_line_to_move(self, move_vals, matching_data, line):
        """Add difference line to move if needed"""
        diff_amount = matching_data.get('diff_amount', 0)
        if abs(matching_data.get('amount_currency_diff', 0.0)) < 0.01:  # Ignore negligible differences
            return
            
        difference_account_param = self.env['ir.config_parameter'].sudo().get_param('account_statement_reconcile.difference_account')
        if not difference_account_param:
            _logger.warning("No difference account configured, skipping difference line")
            return
            
        try:
            difference_account_id = self.env['account.account'].browse(int(difference_account_param))
            # Get the difference amount in company currency
            balance = matching_data.get('amount_currency_diff', 0.0)
            
            if difference_account_id and difference_account_id.exists():
                # Calculate the total debits and credits from existing move lines
                total_debit = sum(line[2].get('debit', 0.0) for line in move_vals.get('line_ids', []))
                total_credit = sum(line[2].get('credit', 0.0) for line in move_vals.get('line_ids', []))
                
                # Determine if we need to add to debit or credit side to balance the entry
                amount_diff = abs(total_debit - total_credit)
                
                if total_debit > total_credit:
                    # Need to add to credit side
                    debit_value = 0.0
                    credit_value = amount_diff
                else:
                    # Need to add to debit side
                    debit_value = amount_diff
                    credit_value = 0.0
                
                # Set amount_currency based on debit/credit direction
                if debit_value > 0:
                    amount_currency_value = abs(diff_amount)
                else:
                    amount_currency_value = -abs(diff_amount)
                
                move_vals['line_ids'].append((0, 0, {
                    'account_id': difference_account_id.id,
                    'name': "Payment difference",
                    'currency_id': line.currency_id.id,
                    'amount_currency': amount_currency_value,
                    'debit': debit_value,
                    'credit': credit_value,
                }))
                _logger.info(
                    "Added payment difference line: Amount=%s, Direction=%s, Debit=%s, Credit=%s", 
                    diff_amount, 
                    "Debit" if debit_value > 0 else "Credit",
                    debit_value,
                    credit_value
                )
            else:
                _logger.warning("Difference account not found. Configured ID: %s", difference_account_param)
        except (ValueError, TypeError) as e:
            _logger.error("Error retrieving difference account: %s", str(e))
        
    def _reconcile_statement_with_moves(self, line, matching_move_lines, move):
        """Reconcile statement line with matching move lines"""
        # Ensure matching_move_lines is always iterable
        lines_to_process = matching_move_lines
        
        # Get all created move lines from the journal entry
        created_move_lines = move.line_ids
        
        # For each original move line, find its counterpart in created move lines for reconciliation
        for orig_line in lines_to_process:
            # Skip if orig_line doesn't have required attributes
            # if not orig_line.account_id or not orig_line.reconciled:
            #     continue
                
            # Only reconcile if the account allows reconciliation and line is not already reconciled
            if orig_line.account_id.reconcile and not orig_line.reconciled:
                # Find matching created move lines with opposite debit/credit and same account
                matching_created_lines = created_move_lines.filtered(
                    lambda l: (l.account_id.id == orig_line.account_id.id and 
                              ((orig_line.debit > 0 and l.credit > 0) or
                               (orig_line.credit > 0 and l.debit > 0)))
                )
                # if matching_created_lines.amount_currency != orig_line.amount_currency:
                #     matching_created_lines |= created_move_lines.filtered(
                #         lambda l: l.account_id.id == int(self.env['ir.config_parameter'].sudo().get_param('account_statement_reconcile.difference_account'))
                #     )
                
                
                # Perform reconciliation if we found matching lines
                if matching_created_lines:
                    to_reconcile = orig_line | matching_created_lines
                    
                    if len(to_reconcile) > 1:
                        try:
                            to_reconcile.reconcile()
                            _logger.info("Successfully reconciled move lines: %s", to_reconcile.ids)
                        except Exception as e:
                            _logger.error("Error during reconciliation: %s", str(e))
    
    
    def _prepare_reconciliation_result_message(self, reconciled_count):
        """Prepare result message for reconciliation"""
        message = _("Successfully reconciled %s lines.") % reconciled_count
        if reconciled_count == 0:
            message = _("No matching transactions found for reconciliation.")
            
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _("Auto Reconciliation"),
                'message': message,
                'sticky': False,
                'type': 'success' if reconciled_count > 0 else 'warning',
                'next': {'type': 'ir.actions.act_window_close'},
            },
        }

    def auto_reconcile_cron(self):
        """
        Method to be called by cron job to perform auto-reconciliation
        for all bank statements with incomplete lines
        """
        statements = self.search([('state', '=', 'incomplete')])
        results = {
            'total_statements': len(statements),
            'total_reconciled': 0,
            'statement_results': []
        }
        
        for statement in statements:
            if statement.reconcile_rule_id and statement.incomplete_line_ids:
                result = statement.auto_reconcile()
                if result.get('params', {}).get('type') == 'success':
                    msg = result.get('params', {}).get('message', '')
                    try:
                        reconciled = int(msg.split(' ')[1])
                        results['total_reconciled'] += reconciled
                        results['statement_results'].append({
                            'statement_id': statement.id,
                            'name': statement.name,
                            'journal': statement.journal_id.name,
                            'reconciled_count': reconciled
                        })
                    except (IndexError, ValueError):
                        pass
        
        if results['total_reconciled'] > 0:
            _logger.info(
                'Auto reconciliation cron completed: processed %s statements, reconciled %s lines',
                results['total_statements'], results['total_reconciled']
            )
        else:
            _logger.info(
                'Auto reconciliation cron completed: processed %s statements, no lines reconciled',
                results['total_statements']
            )
            
        return results
    
    @api.onchange('incomplete_line_ids')
    def _onchange_incomplete_line_ids(self):
        for rec in self:
            if rec.incomplete_line_ids:
                max_line_number = max(rec.incomplete_line_ids.mapped('line_number'))
                for line in rec.incomplete_line_ids:
                    if line.line_number <= 0:
                        line.line_number = max_line_number + 1

    def _process_deposit_settlement_reconciliation(self, line, matching_move_lines):
        """
        Process direct reconciliation for deposit_settlement lines without creating a new move.
        Instead, it manually creates the reconciliation record in the database.
        """
        self.ensure_one()
        
        # Manual reconciliation through SQL for deposit_settlement
        if matching_move_lines:
            # Create a new full_reconcile record
            self.env.cr.execute("""
                INSERT INTO account_full_reconcile (create_date, create_uid, write_date, write_uid)
                VALUES (NOW(), %s, NOW(), %s)
                RETURNING id
            """, (self.env.user.id, self.env.user.id))
            
            full_reconcile_id = self.env.cr.fetchone()[0]
            
            # Update all move lines with the full_reconcile_id
            line_ids = matching_move_lines.ids 
            
            if line_ids:
                self.env.cr.execute("""
                    UPDATE account_move_line
                    SET full_reconcile_id = %s,
                        matching_number = %s,
                        reconciled = TRUE
                    WHERE id IN %s
                """, (full_reconcile_id, full_reconcile_id, tuple(line_ids)))

                # Log the reconciliation
                _logger.info(
                    "Manual reconciliation for deposit_settlement: created full_reconcile_id %s for lines %s",
                    full_reconcile_id, line_ids
                )
                
                # Commit changes to avoid losing the reconciliation if an error occurs later
                self.env.cr.commit()
                
                return True
        return False

