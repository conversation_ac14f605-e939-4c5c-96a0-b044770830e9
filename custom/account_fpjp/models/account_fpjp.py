# -*- coding: utf-8 -*-
import base64
from dateutil.relativedelta import relativedelta
import logging

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError
from odoo.tools.safe_eval import safe_eval, time

_logger = logging.getLogger(__name__)


class AccountFpjp(models.Model):
    _name = 'account.fpjp'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = 'FPJP'

    active = fields.Boolean('Active',default=True)
    name = fields.Char('Transaction No.',default='New',tracking=True,copy=False)
    description = fields.Char('Name',tracking=True,required=True)
    fpjp_type_id = fields.Many2one('account.fpjp.type', string='Type',ondelete='cascade',tracking=True)
    fpjp_type_source = fields.Selection(related='fpjp_type_id.source', string='FPJP Type Source')
    tax_verification_type = fields.Selection(related='fpjp_type_id.tax_verification_type', string='Tax Verification Type')
    fpjp_date = fields.Date('Transaction Date',tracking=True,default=fields.Date.context_today)
    source = fields.Selection([
        ('justification', 'Justification'),
        ('non_justification', 'Non Justification'),
    ], string='Source',tracking=True)
    justification_id = fields.Many2one('account.justification', string='Justification',tracking=True)
    supplier_id = fields.Many2one('res.partner', string='Supplier',required=True)
    payment_reference = fields.Char('Invoice Number')
    invoice_date = fields.Date('Invoice Date',required=True)
    employee_id = fields.Many2one('hr.employee', string='Submitter Name',tracking=True,default=lambda self: self.env.user.employee_id)
    requestor_id = fields.Many2one('hr.employee', string='Requestor Name',tracking=True,required=True,default=lambda self: self.env.user.employee_id)
    unit_id = fields.Many2one('hr.department', string='Unit',tracking=True)
    group_id = fields.Many2one('hr.department', string='Group',tracking=True)
    direktorat_id = fields.Many2one('hr.department', string='Direktorat',tracking=True)
    accounting_date = fields.Date(
        string='Accounting Date',
        compute='_compute_accounting_date',
        store=True,
        required=True,
        readonly=False,
        precompute=True)
    bank_account = fields.Char('Account Number')
    bank_id = fields.Many2one('res.partner.bank','Account Number',tracking=True)
    pay_group = fields.Selection([
        ('inhouse', 'Inhouse'),
        ('other', 'Others'),
        ('non_mt_100', 'Non MT 100'),
        ('digipos_bni', 'DigiPOS BNI'),
        ('digipos_others', 'DigiPOS Others'),
    ], string='Pay Group')
    
    job_id = fields.Many2one('hr.job', string='Job',tracking=True)
    state = fields.Selection([
        ('draft', 'Draft'),
        ('pending_approval', 'Pending Approval'),
        ('approved', 'Approved'),
        ('reject', 'Rejected'),
        ('assign_coa', 'Assign CoA'),
        ('done', 'Done'),
        ('return', 'Return'),
        ('cancel', 'Canceled'),
    ], string='State',default='draft',tracking=True)
    line_ids = fields.One2many("account.fpjp.line", 'account_fpjp_id', string='FPJP Lines')
    attachment_ids = fields.Many2many('ir.attachment', string='Attachment')
    list_id = fields.Many2one('account.payment.term', string='Payment Term')
    bill_line_ids = fields.One2many('account.move', 'fpjp_id', string='Vendor Bill')
    count_bills = fields.Integer(compute='_compute_count_bills', string='Vendor Bill')

    terms_and_condition = fields.Html('Terms and Condition')
    untaxed_amt = fields.Float('Untaxed Amt',compute="_compute_amount",precompute=True)
    tax_amt = fields.Float('Tax Amt',compute="_compute_amount",precompute=True)
    total_amount = fields.Float('Total Amount',compute="_compute_amount",precompute=True)
    amount_currency = fields.Float('Amount Currency',compute="_compute_amount",precompute=True)
    amount_residual = fields.Float('Amount Residual',compute="_compute_amount",precompute=True)

    has_type_cip = fields.Boolean(string='Has Type CIP', compute='_compute_has_type_cip', store=True)

    @api.depends('line_ids.type')
    def _compute_has_type_cip(self):
        for order in self:
            order.has_type_cip = any(line.type == 'cip' for line in order.line_ids)
            
    @api.depends('fpjp_date')
    def _compute_accounting_date(self):
        for fpjp in self:
            fpjp.accounting_date = fpjp.fpjp_date
            if fpjp.fpjp_date and fpjp.fpjp_date.day > 24:
                fpjp.accounting_date = (fpjp.fpjp_date + relativedelta(months=1)).replace(day=1)

    @api.constrains('total_amount', 'line_ids.price_subtotal_base', 'line_ids')
    def _compute_remaining_amount(self):
        for rec in self:
            print('oyyyyyyy')
            if rec.justification_id:
                for line in rec.line_ids:
                    if not line.justification_line_id.id:
                        continue

                    fpjp_ids = self.env['account.fpjp.line'].search([('justification_line_id', '=', line.justification_line_id.id),
                                                                    ('account_fpjp_id.state', 'not in', ['reject', 'return', 'cancel'])])
                    
                    total_fpjp_amount = sum(fpjp.amount_total for fpjp in fpjp_ids) or 0
                    total_fpjp_reserved = line.justification_line_id.amount_currency
                    print('oyyyyyy',total_fpjp_reserved, total_fpjp_amount)
                    if total_fpjp_reserved - total_fpjp_amount < 0:
                        raise ValidationError('Total amount must not be more than the remaining justification')

    @api.onchange('supplier_id')
    def _onchange_supplier_id(self):
        if self.supplier_id:
            self.list_id = self.supplier_id.payment_term_id.id
        else:
            self.list_id = False

    @api.onchange('justification_id')
    def _onchange_justification_id(self):
        if self.justification_id and self.justification_id.start_date and self.justification_id.end_date:
            if not (self.justification_id.start_date <= self.fpjp_date <= self.justification_id.end_date):
                self.justification_id = False
                return {
                    'warning': {
                        'title': _('Warning'),
                        'message': _('FPJP date is not within the justification date range.'),
                    },
                }
                
    # @api.depends('line_ids.tax_ids','line_ids.quantity','line_ids.price_unit')
    def _compute_amount(self):
        for fpjp in self:
            fpjp.tax_amt = sum(line.tax_amt for line in fpjp.line_ids)
            fpjp.untaxed_amt = sum(line.price_subtotal_base for line in fpjp.line_ids)
            fpjp.total_amount = sum(line.amount_total for line in fpjp.line_ids)
            fpjp.amount_currency = sum(line.amount_currency for line in fpjp.line_ids)
            fpjp.amount_residual = sum(line.amount_total for line in fpjp.line_ids)

            # fpjp._compute_remaining_amount()

    
    @api.onchange('requestor_id')
    def _onchange_requestor_id(self):
        if self.requestor_id and self.requestor_id.department_id:
            self.unit_id = self.requestor_id.department_id.get_ancestor_department_by_type(department_type='4_unit')
            self.group_id = self.requestor_id.department_id.get_ancestor_department_by_type(department_type='3_group')
            self.direktorat_id = self.requestor_id.department_id.get_ancestor_department_by_type(department_type='1_dir')
        else:
            self.unit_id = False
            self.group_id = False
            self.direktorat_id = False
    @api.onchange('fpjp_type_id')
    def _onchange_fpjp_type_id(self):
        if self.fpjp_type_id:
            self.source = self.fpjp_type_id.source if self.fpjp_type_id.source != 'manual' else False
            self.justification_id = False if self.source != 'justification' else self.justification_id

    def action_set_draft(self):
        self.write({'state': 'draft'})

    def action_submit(self):
        if not self.payment_reference:
            raise UserError(_('Invoice Number must be filled before submission.'))
        if not self.line_ids:
            raise UserError('Required Lines before submit')
        lines_error = self.line_ids.filtered(lambda x: x.quantity == 0 or x.price_unit == 0)
        if lines_error:
            raise UserError('Quantity or Price Unit cannot 0')
        # if self.justification_id:
        #     self.justification_id.budgetary_position_id.fpjp_reserve_amount += self.total_amount
        self.write({'state': 'pending_approval'})

    def action_assign_coa(self):
        self.write({'state': 'assign_coa'})
    def action_approve(self):
        if not self.line_ids:
            raise UserError(_('FPJP lines must be filled before approval.'))
        self.write({'state': 'approved'})
    def action_return(self):
        self.write({'state': 'return'})
    def action_reject(self):
        self.write({'state': 'reject'})
    def action_cancel(self):
        move = self.env['account.move'].search([('fpjp_id', '=', self.id)], limit=1)

        if self.state == 'done' and move and move.state == 'posted':
            raise ValidationError('Cannot cancel FPJP because bill already posted')
        elif self.state == 'done' and move and move.state == 'draft':
            move.unlink()
            
        self.write({'state': 'cancel'})

        

    def action_done(self):
        if not any(line.coa_id for line in self.line_ids):
            raise UserError(_('FPJP lines must have CoA before approval.'))
        self.write({'state': 'done'})
        self._action_post()

    def action_print_verification_sheet(self):
        return self.env.ref('account_fpjp.action_report_verification_sheet_fpjp').report_action(self)
    def action_print_fpjp_template(self):
        return self.env.ref('account_fpjp.action_report_account_fpjp').report_action(self)

    def action_create_vendor_bill(self):
        self.ensure_one()
        # if self.state != 'approved':
        #     raise UserError(_('FPJP must be approved before creating a vendor bill.'))
        self._action_post()
    
    def _generate_report(self,report_ids):
        res = []
        for report_id in report_ids:
            report_content, report_format = self.env['ir.actions.report']._render_qweb_pdf(report_id, self.ids)
            report_content = base64.b64encode(report_content)
            report_name = ''
            if report_id.print_report_name:
                report_name = safe_eval(
                    report_id.print_report_name,
                    {
                        'object': self,
                        'time': time,
                    }
                )
            else:
                report_name = _('Report')
            extension = "." + report_format
            if not report_name.endswith(extension):
                report_name += extension
            res.append({'attachment': report_content, 'filename': report_name})

        return res

    

    def _action_post(self):
        self.ensure_one()
        move_vals = self._prepare_move_vals()
        move_line_vals = [(0, 0, line._prepare_move_line_vals()) for line in self.line_ids]
        move_vals['invoice_line_ids'] = move_line_vals
        move = self.env['account.move'].with_context(create_non_manual=True).create(move_vals)
        # move._compute_name()
        move._compute_date()

        action = self.env['ir.actions.act_window']._for_xml_id('account.action_move_in_invoice_type')
        action['res_id'] = move.id
        action['view_mode'] = 'form'
        action['views'] = [(self.env.ref('account.view_move_form').id, 'form')]
        return action

    @api.model_create_multi
    def create(self, vals_list):
        for vals in vals_list:
            if vals.get('name', _('New')) == _('New'):
                direktorat = False
                if vals.get('direktorat_id'):
                    direktorat = self.env['hr.department'].browse(vals.get('direktorat_id'))
                
                if direktorat :
                    name = self.env['ir.sequence'].next_by_code('account.fpjp')
                    name = name.replace('TDPG', direktorat.department_code)
                    vals['name'] =  name
                else:
                    raise ValidationError('Please Fill Directorate Code')
        return super().create(vals_list)

    
    def _prepare_move_vals(self):
        self.ensure_one()
        move_vals = {
            'ref': self.name,
            'partner_id': self.supplier_id.id,
            'move_type': 'in_invoice',
            'date': self.accounting_date,
            'fpjp_id': self.id,
            'fpjp_type_id': self.fpjp_type_id.id,
            'invoice_date': self.fpjp_date,
            'invoice_line_ids': [],
        }
        return move_vals

    # @api.depends('bill_lisne_ids')
    def _compute_count_bills(self):
        for fpjp in self:
            fpjp.count_bills = len(fpjp.bill_line_ids)

    def action_view_bill_lines(self):
        self.ensure_one()
        action = self.env['ir.actions.act_window']._for_xml_id('account.action_move_in_invoice_type')
        action['domain'] = [('id', 'in', self.bill_line_ids.ids)]
        action['context'] = {'create': False}
        if len(self.bill_line_ids) == 1:
            action['views'] = [(self.env.ref('account.view_move_form').id, 'form')]
            action['res_id'] = self.bill_line_ids.id
            action['view_mode'] = 'form'
        return action

    @api.constrains('fpjp_date', 'justification_id')
    def _check_date_start(self):
        """
        Validates that the FPJP is not later than the justification end date.

        Raises:
            ValidationError: If FPJP date is greater than justification end date
        """
        for record in self:
            if record.justification_id and record.justification_id.end_date:
                if record.fpjp_date > record.justification_id.end_date:
                    raise ValidationError(_(
                        'Purchase record date (%s) cannot be later than '
                        'the Justification end date (%s)'
                    ) % (record.fpjp_date, record.justification_id.end_date))

    # Justif Related Function
    @api.depends('state')
    def _check_justification_closure(self):
        """
        Check and close justification when all budget is used.
        Triggered on state changes.
        """
        for fpjp in self:
            if fpjp.state in ['approved', 'done'] and fpjp.justification_id:
                other_fpjps = fpjp.search([
                    ('justification_id', '=', fpjp.justification_id.id),
                    ('state', '=', 'approved')
                ])

                total_fpjp = sum(f.amount_currency for f in other_fpjps)

                if total_fpjp >= fpjp.justification_id.budgetary_position_id.planned_amount:
                    fpjp.justification_id.write({'state': 'done'})
