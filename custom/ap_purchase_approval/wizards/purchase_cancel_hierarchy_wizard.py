# -*- coding: utf-8 -*-

from odoo import Command, fields, models, api
from odoo.exceptions import ValidationError
from datetime import datetime


class PurchaseCancelHierarchyWizard(models.TransientModel):
    _name = 'purchase.cancel.hierarchy.wizard'
    _description = 'Purchase Order Cancel Hierarchy Wizard'

    def _domain_to_employee(self):
        return [('id', '!=', self.env.user.employee_id.id)]

    purchase_cancel_id = fields.Many2one(
        'purchase.order.cancel.close', string='Purchase Order Cancel'
    )
    from_employee_id = fields.Many2one('hr.employee', string='From Employee')
    to_employee_id = fields.Many2one(
        'hr.employee', string='To Employee', domain=_domain_to_employee
    )
    note = fields.Char(string='Note')
    employee_domain = fields.Binary(
        compute="_compute_employee_domain"
    )

    @api.depends('purchase_cancel_id')
    def _compute_employee_domain(self):
        """Compute domain for employee field based on justif"""
        for rec in self:
            domain = [('id', 'in', rec.purchase_cancel_id.selected_approver_ids.ids)]

            rec.employee_domain = domain

    def action_approve(self):
        self.purchase_cancel_id.with_context(note=self.note)._approve()

    def action_reject(self):
        self.purchase_cancel_id.with_context(note=self.note)._reject()

    def action_reassign(self):
        # if self.purchase_cancel_id.approval_reassign_ids.filtered(
        #     lambda x: x.to_employee_id == self.to_employee_id
        # ):
        #     raise ValidationError('This employee already reassign by other employee!')

        # if self.purchase_cancel_id.approval_reassign_ids.filtered(
        #     lambda x: x.from_employee_id == self.to_employee_id
        # ):
        #     raise ValidationError('This employee cannot assign approval!')

        check_reassign = self.purchase_cancel_id.approval_reassign_ids.filtered(lambda x: x.to_employee_id == self.from_employee_id)
        if check_reassign:
            check_reassign.write({'to_employee_id': self.to_employee_id.id})

            check_approval = self.purchase_cancel_id.approval_history_detail_ids.filtered(lambda x: x.reassign_employee_id == self.from_employee_id)
            if check_approval:
                check_approval.write({'reassign_employee_id': self.to_employee_id.id})

            check_approval = self.purchase_cancel_id.approval_history_detail_ids.filtered(lambda x: x.reassign_employee_id == x.employee_id)
            if check_approval:
                check_approval.write({'reassign_employee_id': False})

        else:
            vals = {
                'from_employee_id': self.from_employee_id.id,
                'to_employee_id': self.to_employee_id.id,
            }
        
            self.purchase_cancel_id.approval_reassign_ids = [(0, 0, vals)]

            check_approval = self.purchase_cancel_id.approval_history_detail_ids.filtered(lambda x: x.employee_id == self.from_employee_id)
            if check_approval:
                check_approval.write({'reassign_employee_id': self.to_employee_id.id})

        self.purchase_cancel_id._compute_selected_approvers()

        today_date = datetime.now()

        msg = {
            'employee_id': self.env.user.employee_id.id,
            'date': today_date,
            'state': 'reassign',
            'note': f'Reassign approval from {self.from_employee_id.name} to {self.to_employee_id.name}'
        }
        self.purchase_cancel_id.approval_message_ids = [(0, 0, msg)]

        all_approval = self.purchase_cancel_id.approval_history_ids.mapped('approval_employee_ids.id')
        if all_approval and self.to_employee_id.id in all_approval:
            self.purchase_cancel_id.with_context({'note': self.note})._approve(approver=self.to_employee_id)

        check_reassign = self.purchase_cancel_id.approval_reassign_ids.filtered(lambda x: x.from_employee_id == x.to_employee_id)
        if check_reassign:
            check_reassign.unlink()

        # send email
        emails = self.to_employee_id.work_email
        if emails:
            email_to = emails
            template = self.purchase_cancel_id._get_info_template()
            message = f'Reassign approval Cancel Order {self.purchase_cancel_id.name} \
            from employee {self.from_employee_id.name}'

            web_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
            url_root = '%s/' % web_url  
            link = '%sodoo/purchase_cancel/%s' % (url_root, self.purchase_cancel_id.id)
            self.purchase_cancel_id.with_context(message=message, link=link).send_email(email_to, template)