# -*- coding: utf-8 -*-

from odoo import Command, _, api, fields, models
from odoo.exceptions import ValidationError
from datetime import date, datetime


class PurchaseDpl(models.Model):
    _inherit = 'purchase.dpl'

    hierarchy_id = fields.Many2one('base.hierarchy', string='Approval Hierarchy')
    approval_history_ids = fields.One2many(
        'purchase.dpl.approval', 'purchase_dpl_id', string='Approval Histories'
    )
    approval_history_detail_ids = fields.One2many(
        'purchase.dpl.approval.detail', 'purchase_dpl_id', string='Approval History Details'
    )       
    approval_message_ids = fields.One2many(
        'purchase.dpl.message', 'purchase_dpl_id', string='Message Histories'
    )
    approval_reassign_ids = fields.One2many(
        'purchase.dpl.reassign', 'purchase_dpl_id', string='Reassign Employees'
    )
    selected_approver_ids = fields.Many2many(
        'hr.employee',
        string='Selected Approvers',
        compute='_compute_approvers',
        store=True,
    )
    is_current_approver = fields.Boolean(
        string='Current Approver', compute='_compute_approvers'
    )
    is_reassign = fields.Boolean(string='Is Reassign', compute='_compute_approvers')
    requestor_id = fields.Many2one(
        comodel_name='hr.employee',
        string='Requestor Name',
        default=lambda self: self.env['hr.employee'].search([('user_id', '=', self.env.user.id)], limit=1)
    )

    is_admin = fields.Boolean(string='Is Admin',
                                         compute='check_administrator_setting')
    
    def check_administrator_setting(self):
        for rec in self:
            group_admin = rec.sudo().env.ref('justif_approval.administrator_setting')
            if group_admin and group_admin.users and group_admin.users.filtered(lambda x: x.id == self.env.user.id):
                rec.is_admin = True
            else:
                rec.is_admin = False

    def get_current_level(self):
        self.ensure_one()
        current_level = self.approval_history_ids.filtered(
            lambda x: x.state == 'in_progress'
        )
        return current_level and current_level[0] or False

    @api.depends('approval_reassign_ids', 'approval_history_ids', 'state', 'approval_history_detail_ids')
    def _compute_approvers(self):
        for dpl in self:
            line = dpl.get_current_level()
            dpl.selected_approver_ids = dpl.get_employees(line)
            dpl.is_current_approver = (
                self.env.user.employee_id in dpl.selected_approver_ids
            )
            dpl.is_reassign = bool(
                dpl.approval_reassign_ids.filtered(
                    lambda x: x.to_employee_id == self.env.user.employee_id
                )
            )

    def action_request_approve(self):
        super().action_request_approve()
        for dpl in self:
            dpl._assign_approval()

            emails = False
            line = dpl.get_current_level()
            employees = dpl.get_employees(line)
            if employees:
                emails = employees.filtered(lambda x: x.work_email).mapped('work_email')
            if emails:
                email_to = ', '.join(emails)
                template = dpl._get_approval_template()

                web_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
                url_root = '%s/' % web_url  
                link = '%sodoo/purchase_dpl/%s' % (url_root, dpl.id)
                dpl.with_context(link=link).send_email(email_to, template)

    def get_employees(self, line=False, employees=False, employee_id=False):
        if line and line.approval_by == 'position_department':
            if line.approval_type == 'paralel' or (line.approval_type == 'serial' and not line.send_unit) or line.approval_type == 'voting':
                domain_emp = []
                domain_emp.append(('department_id', 'in', line.department_ids.ids))
                domain_emp.append(('job_id', 'in', line.job_ids.ids))

                if employee_id:
                    list_emp = []
                    list_emp.append(employee_id)
                    check_reassign = self.approval_reassign_ids.filtered(lambda x: x.to_employee_id.id == employee_id)
                    if check_reassign:
                        employee_id2 = check_reassign[0].from_employee_id.id
                        list_emp.append(employee_id2)

                    domain_emp.append(('id', 'in', list_emp))

                if line.approval_employee_ids:
                    department_exist = line.approval_employee_ids.mapped('department_id')
                    domain_emp.append(('department_id', 'not in', department_exist.ids))

            else:
                domain_emp = []
                # domain_emp.append(('department_id', 'in', line.department_ids.ids))
                # domain_emp.append(('department_id', '=', self.requestor_id.department_id.id))
                # domain_emp.append(('job_id', 'in', line.job_ids.ids))

                dept_requestor = self.requestor_id.department_id
                domain_job = ('job_id', 'in', line.job_ids.ids)

                continue_check = True
                while continue_check:
                    domain_dept_requestor = ('department_id', '=', dept_requestor.id)
                    employee_check = self.env['hr.employee'].search([domain_dept_requestor, domain_job], limit=1)

                    if employee_check:
                        domain_emp.append(domain_dept_requestor)
                        domain_emp.append(domain_job)
                        continue_check = False
                    elif dept_requestor.parent_id:
                        dept_requestor = dept_requestor.parent_id
                    else:
                        domain_emp.append(('id', '=', False))
                        continue_check = False

                if employee_id:
                    list_emp = []
                    list_emp.append(employee_id)
                    check_reassign = self.approval_reassign_ids.filtered(lambda x: x.to_employee_id.id == employee_id)
                    if check_reassign:
                        employee_id2 = check_reassign[0].from_employee_id.id
                        list_emp.append(employee_id2)

                    domain_emp.append(('id', 'in', list_emp))

                if line.approval_employee_ids:
                    department_exist = line.approval_employee_ids.mapped('department_id')
                    domain_emp.append(('department_id', 'not in', department_exist.ids))

            employees = self.env['hr.employee'].search(domain_emp)

            for emp in self.approval_reassign_ids:
                if emp.from_employee_id in employees:
                    employees -= emp.from_employee_id
                    employees += emp.to_employee_id

        elif line and line.approval_by == 'job_level_department':   
            if line.approval_type == 'paralel' or (line.approval_type == 'serial' and not line.send_unit) or line.approval_type == 'voting':           
                domain_emp = []
                domain_emp.append(('department_id', 'in', line.department_ids.ids))
                domain_emp.append(('job_id.level', '=', line.job_level))

                if employee_id:
                    list_emp = []
                    list_emp.append(employee_id)
                    check_reassign = self.approval_reassign_ids.filtered(lambda x: x.to_employee_id.id == employee_id)
                    if check_reassign:
                        employee_id2 = check_reassign[0].from_employee_id.id
                        list_emp.append(employee_id2)

                    domain_emp.append(('id', 'in', list_emp))

                if line.approval_employee_ids:
                    department_exist = line.approval_employee_ids.mapped('department_id')
                    domain_emp.append(('department_id', 'not in', department_exist.ids))

            else:
                domain_emp = []
                # domain_emp.append(('department_id', 'in', line.department_ids.ids))
                # domain_emp.append(('department_id', '=', self.requestor_id.department_id.id))
                # domain_emp.append(('job_id.level', '=', line.job_level))

                dept_requestor = self.requestor_id.department_id
                domain_level = ('job_id.level', '=', line.job_level)

                continue_check = True
                while continue_check:
                    domain_dept_requestor = ('department_id', '=', dept_requestor.id)
                    employee_check = self.env['hr.employee'].search([domain_dept_requestor, domain_level], limit=1)

                    if employee_check:
                        domain_emp.append(domain_dept_requestor)
                        domain_emp.append(domain_level)
                        continue_check = False
                    elif dept_requestor.parent_id:
                        dept_requestor = dept_requestor.parent_id
                    else:
                        domain_emp.append(('id', '=', False))
                        continue_check = False

                if employee_id:
                    list_emp = []
                    list_emp.append(employee_id)
                    check_reassign = self.approval_reassign_ids.filtered(lambda x: x.to_employee_id.id == employee_id)
                    if check_reassign:
                        employee_id2 = check_reassign[0].from_employee_id.id
                        list_emp.append(employee_id2)

                    domain_emp.append(('id', 'in', list_emp))

                if line.approval_employee_ids:
                    department_exist = line.approval_employee_ids.mapped('department_id')
                    domain_emp.append(('department_id', 'not in', department_exist.ids))

            employees = self.env['hr.employee'].search(domain_emp)

            for emp in self.approval_reassign_ids:
                if emp.from_employee_id in employees:
                    employees -= emp.from_employee_id
                    employees += emp.to_employee_id

        elif line and line.approval_by == 'position': 
            if line.approval_type == 'paralel' or (line.approval_type == 'serial' and not line.send_unit) or line.approval_type == 'voting':   
                domain_emp = []
                domain_emp.append(('job_id', 'in', line.job_ids.ids))

                if employee_id:
                    list_emp = []
                    list_emp.append(employee_id)
                    check_reassign = self.approval_reassign_ids.filtered(lambda x: x.to_employee_id.id == employee_id)
                    if check_reassign:
                        employee_id2 = check_reassign[0].from_employee_id.id
                        list_emp.append(employee_id2)

                    domain_emp.append(('id', 'in', list_emp))

                # if line.approval_employee_ids:
                #     job_exist = line.approval_employee_ids.mapped('job_id')
                #     domain_emp.append(('job_id', 'not in', job_exist.ids))

            else:
                domain_emp = []
                # domain_emp.append(('department_id', '=', self.requestor_id.department_id.id))
                # domain_emp.append(('job_id', 'in', line.job_ids.ids))

                dept_requestor = self.requestor_id.department_id
                domain_job = ('job_id', 'in', line.job_ids.ids)

                continue_check = True
                while continue_check:
                    domain_dept_requestor = ('department_id', '=', dept_requestor.id)
                    employee_check = self.env['hr.employee'].search([domain_dept_requestor, domain_job], limit=1)

                    if employee_check:
                        domain_emp.append(domain_dept_requestor)
                        domain_emp.append(domain_job)
                        continue_check = False
                    elif dept_requestor.parent_id:
                        dept_requestor = dept_requestor.parent_id
                    else:
                        domain_emp.append(('id', '=', False))
                        continue_check = False

                if employee_id:
                    list_emp = []
                    list_emp.append(employee_id)
                    check_reassign = self.approval_reassign_ids.filtered(lambda x: x.to_employee_id.id == employee_id)
                    if check_reassign:
                        employee_id2 = check_reassign[0].from_employee_id.id
                        list_emp.append(employee_id2)

                    domain_emp.append(('id', 'in', list_emp))

                # if line.approval_employee_ids:
                #     job_exist = line.approval_employee_ids.mapped('job_id')
                #     domain_emp.append(('job_id', 'not in', job_exist.ids))

            employees = self.env['hr.employee'].search(domain_emp)

            if line.approval_employee_ids:
                employees = employees - line.approval_employee_ids

            for emp in self.approval_reassign_ids:
                if emp.from_employee_id in employees:
                    employees -= emp.from_employee_id
                    employees += emp.to_employee_id

        elif line and line.approval_by == 'department': 
            if line.approval_type == 'paralel' or (line.approval_type == 'serial' and not line.send_unit) or line.approval_type == 'voting': 
                domain_emp = []
                domain_emp.append(('department_id', 'in', line.department_ids.ids))

                if employee_id:
                    list_emp = []
                    list_emp.append(employee_id)
                    check_reassign = self.approval_reassign_ids.filtered(lambda x: x.to_employee_id.id == employee_id)
                    if check_reassign:
                        employee_id2 = check_reassign[0].from_employee_id.id
                        list_emp.append(employee_id2)

                    domain_emp.append(('id', 'in', list_emp))

                if line.approval_employee_ids:
                    department_exist = line.approval_employee_ids.mapped('department_id')
                    domain_emp.append(('department_id', 'not in', department_exist.ids))

            else:
                domain_emp = []
                # domain_emp.append(('department_id', '=', self.requestor_id.department_id.id))
                # domain_emp.append(('department_id', 'in', line.department_ids.ids))

                dept_requestor = self.requestor_id.department_id
                # domain_job = ('job_id', 'in', line.job_ids.ids)

                continue_check = True
                while continue_check:
                    domain_dept_requestor = ('department_id', '=', dept_requestor.id)
                    employee_check = self.env['hr.employee'].search([domain_dept_requestor], limit=1)

                    if employee_check:
                        domain_emp.append(domain_dept_requestor)
                        # domain_emp.append(domain_job)
                        continue_check = False
                    elif dept_requestor.parent_id:
                        dept_requestor = dept_requestor.parent_id
                    else:
                        domain_emp.append(('id', '=', False))
                        continue_check = False

                if employee_id:
                    list_emp = []
                    list_emp.append(employee_id)
                    check_reassign = self.approval_reassign_ids.filtered(lambda x: x.to_employee_id.id == employee_id)
                    if check_reassign:
                        employee_id2 = check_reassign[0].from_employee_id.id
                        list_emp.append(employee_id2)

                    domain_emp.append(('id', 'in', list_emp))

                if line.approval_employee_ids:
                    department_exist = line.approval_employee_ids.mapped('department_id')
                    domain_emp.append(('department_id', 'not in', department_exist.ids))

            employees = self.env['hr.employee'].search(domain_emp)

            for emp in self.approval_reassign_ids:
                if emp.from_employee_id in employees:
                    employees -= emp.from_employee_id
                    employees += emp.to_employee_id


        elif line and line.approval_by == 'employee': 
            employees = line.employee_ids

            # if employee_id:
            #     employee = self.env['hr.employee'].browse(employee_id)
            #     employees = employees - employee

            if employee_id:
                list_emp = []
                list_emp.append(employee_id)
                check_reassign = self.approval_reassign_ids.filtered(lambda x: x.to_employee_id.id == employee_id)
                if check_reassign:
                    employee_id2 = check_reassign[0].from_employee_id.id
                    list_emp.append(employee_id2)

                # employee = self.env['hr.employee'].browse(list_emp)
                employees = employees.filtered(lambda x: x.id in list_emp)

            if line.approval_employee_ids:
                employees = employees - line.approval_employee_ids

            for emp in self.approval_reassign_ids:
                if emp.from_employee_id in employees:
                    employees -= emp.from_employee_id
                    employees += emp.to_employee_id

        elif line and line.approval_by == 'job_level':
            if line.approval_type == 'paralel' or (line.approval_type == 'serial' and not line.send_unit) or line.approval_type == 'voting':
                domain_emp = []
                domain_emp.append(('job_id.level', '=', line.job_level))

                if employee_id:
                    list_emp = []
                    list_emp.append(employee_id)
                    check_reassign = self.approval_reassign_ids.filtered(lambda x: x.to_employee_id.id == employee_id)
                    if check_reassign:
                        employee_id2 = check_reassign[0].from_employee_id.id
                        list_emp.append(employee_id2)

                    domain_emp.append(('id', 'in', list_emp))
            else:
                domain_emp = []
                # domain_emp.append(('department_id', '=', self.requestor_id.department_id.id))
                # domain_emp.append(('job_id.level', '=', line.job_level))

                dept_requestor = self.requestor_id.department_id
                domain_level = ('job_id.level', '=', line.job_level)

                continue_check = True
                while continue_check:
                    domain_dept_requestor = ('department_id', '=', dept_requestor.id)
                    employee_check = self.env['hr.employee'].search([domain_dept_requestor, domain_level], limit=1)

                    if employee_check:
                        domain_emp.append(domain_dept_requestor)
                        domain_emp.append(domain_level)
                        continue_check = False
                    elif dept_requestor.parent_id:
                        dept_requestor = dept_requestor.parent_id
                    else:
                        domain_emp.append(('id', '=', False))
                        continue_check = False

                if employee_id:
                    list_emp = []
                    list_emp.append(employee_id)
                    check_reassign = self.approval_reassign_ids.filtered(lambda x: x.to_employee_id.id == employee_id)
                    if check_reassign:
                        employee_id2 = check_reassign[0].from_employee_id.id
                        list_emp.append(employee_id2)

                    domain_emp.append(('id', 'in', list_emp))

            employees = self.env['hr.employee'].search(domain_emp)

            if line.approval_employee_ids:
                employees = employees - line.approval_employee_ids

            for emp in self.approval_reassign_ids:
                if emp.from_employee_id in employees:
                    employees -= emp.from_employee_id
                    employees += emp.to_employee_id

        if line and line.approval_type == 'serial' and line.approval_employee_ids:
            employees = False
            
        return employees

    def _get_approval_template(self):
        """function to return mail template for approval"""
        return 'ap_purchase_approval.mail_purchase_dpl_approval'

    def _get_info_template(self):
        """function to return mail template for info"""
        return 'ap_purchase_approval.mail_purchase_dpl_info'

    def _assign_approval(self):
        """helper function to assign approval hierarchy to history"""
        self.ensure_one()
        check_hierarchy = (
            self.env['base.hierarchy']
            .sudo()
            .search([('model_id.model', '=', self._name)])
            .filtered(lambda x: x.minimum_amount <= self.amount <= x.maximum_amount
                        and self.requestor_id.department_id.id in x.department_ids.ids)
        )

        if not check_hierarchy:
            raise ValidationError(
                _('Cannot found hierarchy for %s.\nPlease check your configuration!')
                % self._description
            )

        #self.hierarchy_id = hierarchy.id
        #lines = [Command.clear()]
        #self.approval_reassign_ids = [Command.clear()]

        self.hierarchy_id = check_hierarchy[0].id
        hierarchy = self.hierarchy_id
        
        if hierarchy: 
            lines = [(5, 0, 0)]
            lines_detail = [(5, 0, 0)]
            self.approval_reassign_ids = [(5, 0, 0)] 

            for line in self.hierarchy_id.line_ids.sorted(key=lambda x: x.level):
                state = 'in_progress' if line.level == 1 else False

                vals = {
                    'level': line.level,
                    'approval_by': line.approval_by,
                    'department_ids': line.department_ids,
                    'job_ids': line.job_ids,
                    'job_level': line.job_level,
                    'employee_ids': line.employee_ids,
                    'approval_type': line.approval_type,
                    'voting_point': line.voting_point,
                    'state': state,
                }
                lines.append(Command.create(vals))

            self.approval_history_ids = lines

            for line in self.approval_history_ids:
                employees = self.get_employees(line)
                for employee in employees:
                    vals_detail = {
                        'level': line.level,
                        'employee_id': employee.id,
                        'reassign_employee_id': False,
                        'reassign_employee_date': False,
                        'reassign_employee_state': False,
                        'reassign_employee_note': False
                    }
                    lines_detail.append((0, 0, vals_detail))

            self.approval_history_detail_ids = lines_detail  

            # check complete hierarchy
            self.check_hiearchy()

    def check_hiearchy(self):
        base_hierarchy = self.approval_history_ids.mapped('level')
        detail_hierarchy = self.approval_history_detail_ids.mapped('level')

        if not base_hierarchy or not detail_hierarchy:
            raise ValidationError('Not complete hierarchy, please check your configuration!')
        if set(detail_hierarchy) != set(base_hierarchy):
            raise ValidationError('Not complete hierarchy, please check your configuration!')          

    def action_approval(self):
        ctx = self.env.context
        return {
            'type': 'ir.actions.act_window',
            'res_model': 'purchase.dpl.hierarchy.wizard',
            'view_mode': 'form',
            'name': _("Approval Action"),
            'target': 'new',
            'context': {
                'readonly': True if not self.is_admin else False,
                'default_from_employee_id': self.env.user.employee_id.id if not self.is_admin else False,
                'default_purchase_dpl_id': self.id,
                'default_employee_domain': [('id', 'in', self.selected_approver_ids.ids)],
                'approve': ctx.get('approve'),
                'reject': ctx.get('reject'),
                'reassign': ctx.get('reassign'),
            },
        }

    def _check_is_complete_approval_line(self, line):
        self.ensure_one()
        if line.approval_type == 'voting':
            is_complete = (
                True if line.total_voting_point >= line.voting_point else False
            )
        else:
            is_complete = not bool(self.get_employees(line, True))

        return is_complete

    def _not_complete_approval(self):
        is_complete = bool(
            self.approval_history_ids.filtered(
                lambda x: x.state not in ['approve', 'reject']
            )
        )
        return is_complete

    def _approve(self, approver=False):
        for rec in self:
            today_date = datetime.now()
            line = rec.get_current_level()
            approver_employee = approver or self.env.user.employee_id
            
            check_reassign = rec.approval_reassign_ids.filtered(lambda x: x.to_employee_id == approver_employee)
            if check_reassign:
                # check_reassign = check_reassign[0]
                from_employee_id = False

                for reassign in check_reassign:
                    check_employee_exist_reassing = rec.get_employees(line, employee_id=reassign.from_employee_id.id)
                    if check_employee_exist_reassing:
                        from_employee_id = reassign.from_employee_id

                if from_employee_id:
                    line.write({'approval_employee_ids': [(4, from_employee_id.id)]})
                    line.total_voting_point += from_employee_id.voting_point

                # check details approval normal
                check_detail = rec.approval_history_detail_ids.filtered(lambda x: x.employee_id == approver_employee and not x.employee_note)
                if check_detail:
                    vals = {
                        'employee_state': 'approve',
                        'employee_note': self.env.context.get('note'),
                        'employee_date': today_date
                    }
                    check_detail.write(vals)

                # check details approval reassign
                check_detail = rec.approval_history_detail_ids.filtered(lambda x: x.reassign_employee_id == approver_employee and not x.reassign_employee_note)
                if check_detail:
                    vals = {
                        'reassign_employee_state': 'approve',
                        'reassign_employee_note': self.env.context.get('note'),
                        'reassign_employee_date': today_date
                    }
                    check_detail.write(vals)

                check_employee_exist_reassing = rec.get_employees(line, employee_id=approver_employee.id)
                if check_employee_exist_reassing:
                    line.write({'approval_employee_ids': [(4, approver_employee.id)]})
                    line.total_voting_point += approver_employee.voting_point
                
            else:
                line.write({'approval_employee_ids': [(4, approver_employee.id)]})
                line.total_voting_point += approver_employee.voting_point

                # check details approval normal
                check_detail = rec.approval_history_detail_ids.filtered(lambda x: x.employee_id == approver_employee and not x.employee_note)
                if check_detail:
                    vals = {
                        'employee_state': 'approve',
                        'employee_note': self.env.context.get('note'),
                        'employee_date': today_date
                    }
                    check_detail.write(vals)

                # check details approval reassign
                check_detail = rec.approval_history_detail_ids.filtered(lambda x: x.reassign_employee_id == approver_employee and not x.reassign_employee_note)
                if check_detail:
                    vals = {
                        'reassign_employee_state': 'approve',
                        'reassign_employee_note': self.env.context.get('note'),
                        'reassign_employee_date': today_date
                    }
                    check_detail.write(vals)

            if not approver:
                msg = {
                    'employee_id': approver_employee.id,
                    'date': today_date,
                    'state': 'approve',
                    'note': self.env.context.get('note')
                }
                self.approval_message_ids = [(0, 0, msg)]

            level = line.level
            line_check = rec.approval_history_ids.filtered(lambda x: x.level == level)
            while line_check:
                check_employee_exist = rec.get_employees(line_check, employee_id=approver_employee.id)
                if check_employee_exist:
                    check_reassign = rec.approval_reassign_ids.filtered(lambda x: x.to_employee_id == approver_employee)
                    if check_reassign:
                        # check_reassign = check_reassign[0]
                        from_employee_id = False

                        for reassign in check_reassign:
                            check_employee_exist_reassing = rec.get_employees(line_check, employee_id=reassign.from_employee_id.id)
                            if check_employee_exist_reassing:
                                from_employee_id = reassign.from_employee_id

                        if from_employee_id:
                            line_check.write({'approval_employee_ids': [(4, from_employee_id.id)]})
                            line_check.total_voting_point += from_employee_id.voting_point

                        # check details approval normal
                        check_detail = rec.approval_history_detail_ids.filtered(lambda x: x.employee_id == approver_employee and not x.employee_state)
                        if check_detail:
                            vals = {
                                'employee_state': 'approve',
                                'employee_note': self.env.context.get('note'),
                                'employee_date': today_date
                            }
                            check_detail.write(vals)

                        # check details approval reassign
                        check_detail = rec.approval_history_detail_ids.filtered(lambda x: x.reassign_employee_id == approver_employee and not x.reassign_employee_note)
                        if check_detail:
                            vals = {
                                'reassign_employee_state': 'approve',
                                'reassign_employee_note': self.env.context.get('note'),
                                'reassign_employee_date': today_date
                            }
                            check_detail.write(vals)

                        check_employee_exist_reassing = rec.get_employees(line_check, employee_id=approver_employee.id)
                        if check_employee_exist_reassing:
                            line_check.write({'approval_employee_ids': [(4, approver_employee.id)]})
                            line_check.total_voting_point += approver_employee.voting_point
                        
                    else:
                        line_check.write({'approval_employee_ids': [(4, approver_employee.id)]})
                        line_check.total_voting_point += approver_employee.voting_point

                        # check details approval normal
                        check_detail = rec.approval_history_detail_ids.filtered(lambda x: x.employee_id == approver_employee and not x.employee_state)
                        if check_detail:
                            vals = {
                                'employee_state': 'approve',
                                'employee_note': self.env.context.get('note'),
                                'employee_date': today_date
                            }
                            check_detail.write(vals)

                        # check details approval reassign
                        check_detail = rec.approval_history_detail_ids.filtered(lambda x: x.reassign_employee_id == approver_employee and not x.reassign_employee_note)
                        if check_detail:
                            vals = {
                                'reassign_employee_state': 'approve',
                                'reassign_employee_note': self.env.context.get('note'),
                                'reassign_employee_date': today_date
                            }
                            check_detail.write(vals)

                before_line = bool(
                    rec.approval_history_ids.filtered(
                        lambda x: x.level < line_check.level
                        and x.state not in ['approve', 'reject']
                    )
                )
                if rec._check_is_complete_approval_line(line_check) and not before_line:
                    line_check.write({'state': 'approve'})

                    next_level = line_check.level + 1
                    next_line = rec.approval_history_ids.filtered(
                        lambda x: x.level == next_level
                    )
                    if next_line:
                        next_line.state = 'in_progress'

                        # send email
                        emails = False
                        employees = rec.get_employees(next_line)
                        if employees:
                            emails = employees.filtered(lambda x: x.work_email).mapped(
                                'work_email'
                            )
                        if emails:
                            email_to = ', '.join(emails)
                            template = rec._get_approval_template()

                            web_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
                            url_root = '%s/' % web_url  
                            link = '%sodoo/purchase_dpl/%s' % (url_root, self.id)
                            rec.with_context(link=link).send_email(email_to, template)

                # next check
                next_level_check = line_check.level + 1
                line_check = rec.approval_history_ids.filtered(
                    lambda x: x.level == next_level_check
                )

            check_note_exist = rec.approval_history_detail_ids.filtered(lambda x: (x.reassign_employee_id == approver_employee or x.employee_id == approver_employee) and (x.employee_note or x.reassign_employee_note))
            if check_note_exist:
                check_note_exist = check_note_exist[0]
                note = check_note_exist.employee_note or check_note_exist.reassign_employee_note

                # check details approval normal
                check_detail = rec.approval_history_detail_ids.filtered(lambda x: x.employee_id == approver_employee and not x.employee_state)
                if check_detail:
                    vals = {
                        'employee_note': note,
                    }
                    check_detail.write(vals)

                # check details approval reassign
                check_detail = rec.approval_history_detail_ids.filtered(lambda x: x.reassign_employee_id == approver_employee and not x.reassign_employee_note)
                if check_detail:
                    vals = {
                        'reassign_employee_note': note,
                    }
                    check_detail.write(vals)

            if not rec._not_complete_approval():
                # main function for approve
                rec.action_approve()

                # send email
                emails = rec.requestor_id.work_email
                if emails:
                    email_to = emails
                    template = rec._get_info_template()
                    message = f'DPL {rec.name} has been approved'

                    web_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
                    url_root = '%s/' % web_url  
                    link = '%sodoo/purchase_dpl/%s' % (url_root, self.id)
                    rec.with_context(message=message, link=link).send_email(email_to, template)

    def _reject(self):
        for dpl in self:
            line = dpl.get_current_level()
            line.write({'state': 'reject'})

            today_date = datetime.now()

            # check details approval normal
            check_detail = dpl.approval_history_detail_ids.filtered(lambda x: x.employee_id == self.env.user.employee_id)
            if check_detail:
                vals = {
                    'employee_state': 'reject',
                    'employee_note': self.env.context.get('note'),
                    'employee_date': today_date
                }
                check_detail.write(vals)

            # check details approval reassign
            check_detail = dpl.approval_history_detail_ids.filtered(lambda x: x.reassign_employee_id == self.env.user.employee_id)
            if check_detail:
                vals = {
                    'reassign_employee_state': 'reject',
                    'reassign_employee_note': self.env.context.get('note'),
                    'reassign_employee_date': today_date
                }
                check_detail.write(vals)

            message_vals = {
                'employee_id': self.env.user.employee_id.id,
                'date': fields.Datetime.now(),
                'state': 'reject',
                'note': self.env.context.get('note'),
            }
            dpl.approval_message_ids = [Command.create(message_vals)]
            dpl.action_reject()

            # send email
            emails = dpl.requestor_id.work_email
            if emails:
                email_to = emails
                template = dpl._get_info_template()
                message = f'DPL {dpl.name} has been rejected'

                web_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
                url_root = '%s/' % web_url  
                link = '%sodoo/purchase_dpl/%s' % (url_root, self.id)
                dpl.with_context(message=message, link=link).send_email(email_to, template)

    def send_email(self, email_to, template):
        """ function to send info email to requestor """
        message = self.env.context.get('message')
        link = self.env.context.get('link')
        mail_to = email_to
        try:
            template_id = self.sudo().env.ref('%s' % template)
        except ValueError:
            template_id = False

        # different_date = self._context.get('different_date', 0)

        if mail_to and template_id:
            # template_id.attachment_ids = attachment
            template_id.with_context(
                mail_to=mail_to,
                message=message,
                link=link
            ).send_mail(self.id, force_send=True)
