# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError


class TimeDepositReportWizard(models.TransientModel):
    _name = 'time.deposit.report.wizard'
    _description = 'Time Deposit Report Wizard'

    # Filter fields
    business_unit_id = fields.Many2one(
        'res.company',
        string='Business Unit',
        default=lambda self: self.env.company,
        required=True
    )
    start_date = fields.Date(
        string='Start Date',
        required=True,
        default=lambda self: fields.Date.context_today(self).replace(day=1)
    )
    end_date = fields.Date(
        string='End Date',
        required=True,
        default=lambda self: fields.Date.context_today(self)
    )
    product = fields.Selection([
        ('all', 'All'),
        ('syariah', 'Syariah'),
        ('konven', 'Konven'),
        ('merchant', 'Merchant')
    ], string='Product', default='all', required=True)

    deposit_type = fields.Selection([
        ('all', 'All'),
        ('ccredit', 'Corporate Credit Card'),
        ('doc', 'DOC'),
        ('deposit', 'Deposit'),
        ('mma', 'MMA'),
        ('optima', 'Optima')
    ], string='Deposit Type', default='all', required=True)

    bank_account = fields.Many2one(
        'account.journal',
        string='Bank Account',
        domain="[('is_time_deposit', '=', True)]",
        help='Select specific bank account or leave empty for all'
    )

    # Removed date check constraint as requested

    @api.constrains('start_date', 'end_date')
    def _check_dates(self):
        for record in self:
            if record.start_date > record.end_date:
                raise UserError(_('Start Date cannot be greater than End Date'))

    def action_generate_excel_report(self):
        """Generate Excel report for time deposits using raw SQL"""

        # Check if data exists using raw SQL
        if not self._check_data_exists():
            raise UserError(_('No data found for the selected criteria'))

        # Generate Excel report
        return self._generate_xlsx_report()

    def _check_data_exists(self):
        """Check if data exists using raw SQL query"""

        # Build SQL query to check data existence
        sql_query = """
            SELECT COUNT(*) as count
            FROM time_deposit
            WHERE company_id = %s
            AND state != 'extend'
        """

        query_params = [self.business_unit_id.id]

        # Add date filters
        if self.start_date:
            sql_query += " AND open_date >= %s"
            query_params.append(self.start_date)
        if self.end_date:
            sql_query += " AND open_date <= %s"
            query_params.append(self.end_date)

        # Add product filter
        if self.product != 'all':
            sql_query += " AND deposit_product = %s"
            query_params.append(self.product)

        # Add deposit type filter
        if self.deposit_type != 'all':
            sql_query += " AND deposit_type = %s"
            query_params.append(self.deposit_type)

        # Add bank account filter
        if self.bank_account:
            sql_query += " AND deposit_partner_bank_id = %s"
            query_params.append(self.bank_account.id)

        # Execute query
        self.env.cr.execute(sql_query, tuple(query_params))
        result = self.env.cr.fetchone()

        return result[0] > 0 if result else False

    def _generate_xlsx_report(self):
        """Generate XLSX report"""
        # Build URL parameters
        params = {
            'business_unit_id': self.business_unit_id.id,
            'start_date': self.start_date,
            'end_date': self.end_date,
            'product': self.product,
            'deposit_type': self.deposit_type,
        }

        # Add bank account parameter if selected
        if self.bank_account:
            params['bank_account'] = self.bank_account.id
        else:
            params['bank_account'] = 'all'

        # Build URL string
        url_params = '&'.join([f'{k}={v}' for k, v in params.items()])

        # Create Excel report using controller
        return {
            'type': 'ir.actions.act_url',
            'url': f'/time_deposit/excel_report?{url_params}',
            'target': 'self',
        }
