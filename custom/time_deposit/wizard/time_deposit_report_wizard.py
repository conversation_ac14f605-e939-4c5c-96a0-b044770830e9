# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError


class TimeDepositReportWizard(models.TransientModel):
    _name = 'time.deposit.report.wizard'
    _description = 'Time Deposit Report Wizard'

    # Filter fields
    business_unit_id = fields.Many2one(
        'res.company',
        string='Business Unit',
        default=lambda self: self.env.company,
        required=True
    )
    start_date = fields.Date(
        string='Start Date',
        required=True,
        default=lambda self: fields.Date.context_today(self).replace(day=1)
    )
    end_date = fields.Date(
        string='End Date',
        required=True,
        default=lambda self: fields.Date.context_today(self)
    )
    product = fields.Selection([
        ('all', 'All'),
        ('syariah', 'Syariah'),
        ('konven', 'Konven'),
        ('merchant', 'Merchant')
    ], string='Product', default='all', required=True)

    deposit_type = fields.Selection([
        ('all', 'All'),
        ('ccredit', 'Corporate Credit Card'),
        ('doc', 'DOC'),
        ('deposit', 'Deposit'),
        ('mma', 'MMA'),
        ('optima', 'Optima')
    ], string='Deposit Type', default='all', required=True)

    bank_account = fields.Many2one(
        'account.journal',
        string='Bank Account',
        domain="[('is_time_deposit', '=', True)]",
        help='Select specific bank account or leave empty for all'
    )

    # Removed date check constraint as requested

    @api.constrains('start_date', 'end_date')
    def _check_dates(self):
        for record in self:
            if record.start_date > record.end_date:
                raise UserError(_('Start Date cannot be greater than End Date'))

    def action_generate_excel_report(self):
        """Generate Excel report for time deposits"""
        # Build domain for filtering
        domain = [
            ('company_id', '=', self.business_unit_id.id),
            ('state', '!=', 'extend'),  # Exclude deposits with extend status
        ]

        # Add date range filters if set
        if self.start_date:
            domain.append(('open_date', '>=', self.start_date))
        if self.end_date:
            domain.append(('open_date', '<=', self.end_date))

        # Add product filter
        if self.product != 'all':
            domain.append(('deposit_product', '=', self.product))

        # Add deposit type filter
        if self.deposit_type != 'all':
            domain.append(('deposit_type', '=', self.deposit_type))

        # Add bank account filter
        if self.bank_account:
            domain.append(('deposit_partner_bank_id', '=', self.bank_account.id))

        # Get time deposit records
        time_deposits = self.env['time.deposit'].search(domain, order='open_date asc')

        if not time_deposits:
            raise UserError(_('No data found for the selected criteria'))

        # Generate Excel report
        return self._generate_xlsx_report()

    def _generate_xlsx_report(self):
        """Generate XLSX report"""
        # Build URL parameters
        params = {
            'business_unit_id': self.business_unit_id.id,
            'start_date': self.start_date,
            'end_date': self.end_date,
            'product': self.product,
            'deposit_type': self.deposit_type,
        }

        # Add bank account parameter if selected
        if self.bank_account:
            params['bank_account'] = self.bank_account.id
        else:
            params['bank_account'] = 'all'

        # Build URL string
        url_params = '&'.join([f'{k}={v}' for k, v in params.items()])

        # Create Excel report using controller
        return {
            'type': 'ir.actions.act_url',
            'url': f'/time_deposit/excel_report?{url_params}',
            'target': 'self',
        }
