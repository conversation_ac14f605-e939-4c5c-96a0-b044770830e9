<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Time Deposit Report Wizard Form View -->
        <record id="view_time_deposit_report_wizard_form" model="ir.ui.view">
            <field name="name">time.deposit.report.wizard.form</field>
            <field name="model">time.deposit.report.wizard</field>
            <field name="arch" type="xml">
                <form string="Time Deposit Report">
                    <sheet>
                        <group>
                            <group string="Filter Options">
                                <field name="business_unit_id" options="{'no_create': True, 'no_edit': True}"/>
                                <field name="start_date"/>
                                <field name="end_date"/>
                                <field name="product"/>
                                <field name="deposit_type"/>
                                <field name="journal_deposit_type"/>
                            </group>
                        </group>
                    </sheet>
                    <footer>
                        <button name="action_generate_excel_report" 
                                string="Generate Excel Report" 
                                type="object" 
                                class="btn-primary"/>
                        <button string="Cancel" class="btn-secondary" special="cancel"/>
                    </footer>
                </form>
            </field>
        </record>

        <!-- Time Deposit Report Wizard Action -->
        <record id="action_time_deposit_report_wizard" model="ir.actions.act_window">
            <field name="name">Time Deposit Report</field>
            <field name="res_model">time.deposit.report.wizard</field>
            <field name="view_mode">form</field>
            <field name="target">new</field>
            <field name="view_id" ref="view_time_deposit_report_wizard_form"/>
        </record>

    </data>
</odoo>
