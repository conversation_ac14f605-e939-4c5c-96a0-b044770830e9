# -*- coding: utf-8 -*-

from odoo import http
from odoo.http import request, content_disposition
import io
import xlsxwriter
import logging
from datetime import datetime


class TimeDepositReportController(http.Controller):

    @http.route('/time_deposit/excel_report', type='http', auth='user', csrf=False)
    def generate_time_deposit_excel_report(self, **kwargs):
        """Generate Excel report for time deposits"""

        # Get parameters from request
        business_unit_id = int(kwargs.get('business_unit_id', 0))
        start_date = kwargs.get('start_date', '')
        end_date = kwargs.get('end_date', '')
        product = kwargs.get('product', 'all')
        deposit_type = kwargs.get('deposit_type', 'all')
        deposit_type_filter = kwargs.get('deposit_type_filter', 'all')

        # Build base domain for filtering
        base_domain = [
            ('company_id', '=', business_unit_id),
            ('state', '!=', 'extend'),  # Exclude deposits with extend status
        ]

        # Add date range filtering on open_date field
        if start_date:
            base_domain.append(('open_date', '>=', start_date))
        if end_date:
            base_domain.append(('open_date', '<=', end_date))

        # Check if both filters are 'all' - then create 4 separate tables
        if product == 'all' and deposit_type == 'all' and deposit_type_filter == 'all':
            return self._generate_multiple_tables_report(
                base_domain, business_unit_id, start_date, end_date
            )

        # Original single table logic for specific filters
        domain = base_domain.copy()

        # Add product filter using deposit_product field
        if product != 'all':
            domain.append(('deposit_product', '=', product))

        # Add deposit type filter using deposit_type field
        if deposit_type != 'all':
            domain.append(('deposit_type', '=', deposit_type))

        # Add journal deposit type filter using journal's deposit_type field
        if deposit_type_filter != 'all':
            domain.append(('deposit_partner_bank_id.deposit_type', '=', deposit_type_filter))

        # Get time deposit records using raw SQL
        time_deposits = self._get_time_deposits_raw(
            business_unit_id, start_date, end_date, product, deposit_type, deposit_type_filter
        )

        # Check if no data found
        if not time_deposits:
            # Return a simple Excel with "No data found" message
            output = io.BytesIO()
            workbook = xlsxwriter.Workbook(output, {'in_memory': True})
            sheet = workbook.add_worksheet('Time Deposit Report')

            cell_format = workbook.add_format({
                'bold': True,
                'align': 'center',
                'valign': 'vcenter'
            })

            sheet.write(0, 0, 'No data found for the selected criteria', cell_format)
            workbook.close()
            output.seek(0)

            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f'time_deposit_report_{timestamp}.xlsx'

            response = request.make_response(
                output.read(),
                headers=[
                    ('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'),
                    ('Content-Disposition', content_disposition(filename))
                ]
            )
            return response

        # Get business unit name
        business_unit = request.env['res.company'].sudo().browse(business_unit_id)
        business_unit_name = business_unit.name if business_unit else ''
        
        # Get product and deposit type labels
        product_dict = dict(request.env['time.deposit']._fields['deposit_product'].selection)
        deposit_type_dict = dict(request.env['time.deposit']._fields['deposit_type'].selection)

        # Simple labels for specific product filters
        if product == 'konven':
            product_label = 'Konven'
        elif product == 'merchant':
            product_label = 'Merchant'
        elif product == 'syariah':
            product_label = 'Syariah'
        elif product == 'all':
            product_label = 'All'
        else:
            product_label = product_dict.get(product, product)

        deposit_type_label = deposit_type_dict.get(deposit_type, 'All') if deposit_type != 'all' else 'All'
        
        # Prepare Excel file
        output = io.BytesIO()
        workbook = xlsxwriter.Workbook(output, {'in_memory': True})
        sheet = workbook.add_worksheet('Time Deposit Report')
        
        # Define formats
        title_format = workbook.add_format({
            'bold': True,
            'font_size': 16,
            'align': 'center',
            'valign': 'vcenter'
        })
        
        header_format = workbook.add_format({
            'bold': True,
            'bg_color': '#D3D3D3',
            'border': 1,
            'align': 'center',
            'valign': 'vcenter',
            'text_wrap': True
        })
        
        cell_format = workbook.add_format({
            'border': 1,
            'align': 'left',
            'valign': 'vcenter'
        })
        
        number_format = workbook.add_format({
            'border': 1,
            'align': 'right',
            'valign': 'vcenter',
            'num_format': '#,##0.00'
        })
        
        # Set column widths
        sheet.set_column('A:A', 5)   # No
        sheet.set_column('B:B', 15)  # Deposit Number
        sheet.set_column('C:C', 12)  # Open Date
        sheet.set_column('D:D', 12)  # Mature Date
        sheet.set_column('E:E', 12)  # Deposit In Days
        sheet.set_column('F:F', 15)  # Bank
        sheet.set_column('G:G', 15)  # Amount
        sheet.set_column('H:H', 12)  # Interest Rate
        sheet.set_column('I:I', 15)  # Beneficiary Bank
        sheet.set_column('J:J', 15)  # Bank Account
        sheet.set_column('K:K', 10)  # Type
        sheet.set_column('L:L', 15)  # No Bilyet Deposito
        sheet.set_column('M:M', 12)  # Classification
        sheet.set_column('N:N', 15)  # Remarks
        sheet.set_column('O:O', 12)  # Breakable %
        sheet.set_column('P:P', 15)  # Deposit Status
        
        # Write title
        sheet.merge_range('A1:P1', 'List Time Deposit Report', title_format)

        # Write filter information
        row = 2
        sheet.write(row, 0, f'Business Unit: {business_unit_name}', cell_format)
        row += 1
        sheet.write(row, 0, f'Start Date: {start_date}', cell_format)
        row += 1
        sheet.write(row, 0, f'End Date: {end_date}', cell_format)
        row += 1
        sheet.write(row, 0, f'Product: {product_label}', cell_format)
        row += 1
        sheet.write(row, 0, f'Deposit Type: {deposit_type_label}', cell_format)

        # Skip a row
        row += 2

        # Write headers
        headers = [
            'No', 'Deposit Number', 'Open Date', 'Mature Date', 'Deposit In Days',
            'Bank', 'Amount', 'Interest Rate', 'Beneficiary Bank', 'Bank Account',
            'Type', 'No Bilyet Deposito', 'Classification', 'Remarks', 'Breakable %',
            'Deposit Status'
        ]

        for col, header in enumerate(headers):
            sheet.write(row, col, header, header_format)

        # Write data
        row += 1
        for idx, deposit in enumerate(time_deposits, 1):
            sheet.write(row, 0, idx, cell_format)
            sheet.write(row, 1, deposit.get('name', ''), cell_format)

            # Open Date
            if deposit.get('open_date'):
                open_date = deposit['open_date']
                if hasattr(open_date, 'strftime'):
                    sheet.write(row, 2, open_date.strftime('%d-%m-%Y'), cell_format)
                else:
                    sheet.write(row, 2, str(open_date), cell_format)
            else:
                sheet.write(row, 2, '', cell_format)

            # Mature Date
            if deposit.get('maturity_date'):
                maturity_date = deposit['maturity_date']
                if hasattr(maturity_date, 'strftime'):
                    sheet.write(row, 3, maturity_date.strftime('%d-%m-%Y'), cell_format)
                else:
                    sheet.write(row, 3, str(maturity_date), cell_format)
            else:
                sheet.write(row, 3, '', cell_format)

            sheet.write(row, 4, deposit.get('deposit_in_days_placement', 0), cell_format)
            sheet.write(row, 5, self._format_bank_name(deposit.get('bank_name', '')), cell_format)
            sheet.write(row, 6, deposit.get('deposit_original_amount', 0), number_format)
            sheet.write(row, 7, deposit.get('interest_rate', 0), number_format)

            # Beneficiary Bank
            beneficiary_bank = self._format_bank_name(deposit.get('beneficiary_bank_name', ''))
            sheet.write(row, 8, beneficiary_bank, cell_format)
            sheet.write(row, 9, deposit.get('bank_account', ''), cell_format)

            # Type (deposit_type)
            sheet.write(row, 10, deposit_type_dict.get(deposit.get('deposit_type'), ''), cell_format)

            sheet.write(row, 11, '', cell_format)  # No Bilyet Deposito - empty for now
            sheet.write(row, 12, deposit.get('deposit_clasification', ''), cell_format)
            sheet.write(row, 13, deposit.get('deposit_note', ''), cell_format)
            sheet.write(row, 14, '', cell_format)  # Breakable % - empty for now

            # Deposit Status (state)
            state_dict = {
                'draft': 'Draft',
                'placement': 'Placement',
                'extend': 'Extend',
                'withdraw': 'Withdraw',
                'cancel': 'Cancel'
            }
            sheet.write(row, 15, state_dict.get(deposit.get('state'), ''), cell_format)

            row += 1

        # Close workbook and prepare response
        workbook.close()
        output.seek(0)
        
        # Generate filename with timestamp
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'time_deposit_report_{timestamp}.xlsx'
        
        # Create response
        response = request.make_response(
            output.read(),
            headers=[
                ('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'),
                ('Content-Disposition', content_disposition(filename))
            ]
        )
        
        return response

    def _get_time_deposits_raw(self, business_unit_id, start_date, end_date,
                              product, deposit_type, deposit_type_filter):
        """Get time deposit records using raw SQL query"""

        # Build base SQL query
        sql_query = """
            SELECT
                td.id,
                td.name,
                td.open_date,
                td.maturity_date,
                td.deposit_in_days_placement,
                td.deposit_original_amount,
                td.interest_rate,
                td.bank_account,
                td.deposit_clasification,
                td.deposit_note,
                td.state,
                td.deposit_type,
                td.deposit_product,
                td.deposit_partner_bank_id,
                td.beneficiary_bank_id,
                rb.name as bank_name,
                rb2.name as beneficiary_bank_name,
                aj.deposit_type as journal_deposit_type
            FROM time_deposit td
            LEFT JOIN account_journal aj ON td.deposit_partner_bank_id = aj.id
            LEFT JOIN res_bank rb ON aj.bank_id = rb.id
            LEFT JOIN account_journal aj2 ON td.beneficiary_bank_id = aj2.id
            LEFT JOIN res_bank rb2 ON aj2.bank_id = rb2.id
            WHERE td.company_id = %s
            AND td.state != 'extend'
        """

        query_params = [business_unit_id]

        # Add date filters
        if start_date:
            sql_query += " AND td.open_date >= %s"
            query_params.append(start_date)
        if end_date:
            sql_query += " AND td.open_date <= %s"
            query_params.append(end_date)

        # Add product filter
        if product != 'all':
            sql_query += " AND td.deposit_product = %s"
            query_params.append(product)

        # Add deposit type filter
        if deposit_type != 'all':
            sql_query += " AND td.deposit_type = %s"
            query_params.append(deposit_type)

        # Add journal deposit type filter
        if deposit_type_filter != 'all':
            sql_query += " AND aj.deposit_type = %s"
            query_params.append(deposit_type_filter)

        sql_query += " ORDER BY td.open_date ASC"

        # Execute query
        request.env.cr.execute(sql_query, tuple(query_params))
        results = request.env.cr.dictfetchall()

        return results

    def _get_table_data_raw(self, config, business_unit_id, start_date, end_date):
        """Get table data using raw SQL based on configuration"""

        # Build SQL query for specific table
        sql_query = """
            SELECT
                td.id,
                td.name,
                td.open_date,
                td.maturity_date,
                td.deposit_in_days_placement,
                td.deposit_original_amount,
                td.interest_rate,
                td.bank_account,
                td.deposit_clasification,
                td.deposit_note,
                td.state,
                td.deposit_type,
                td.deposit_product,
                td.deposit_partner_bank_id,
                td.beneficiary_bank_id,
                rb.name as bank_name,
                rb2.name as beneficiary_bank_name,
                aj.deposit_type as journal_deposit_type
            FROM time_deposit td
            LEFT JOIN account_journal aj ON td.deposit_partner_bank_id = aj.id
            LEFT JOIN res_bank rb ON aj.bank_id = rb.id
            LEFT JOIN account_journal aj2 ON td.beneficiary_bank_id = aj2.id
            LEFT JOIN res_bank rb2 ON aj2.bank_id = rb2.id
            WHERE td.company_id = %s
            AND td.state != 'extend'
        """

        query_params = [business_unit_id]

        # Add date filters
        if start_date:
            sql_query += " AND td.open_date >= %s"
            query_params.append(start_date)
        if end_date:
            sql_query += " AND td.open_date <= %s"
            query_params.append(end_date)

        # Add specific filters based on config
        if config.get('deposit_type_filter') == 'usd':
            # Return empty for USD (not implemented yet)
            return []
        elif config.get('deposit_type_filter'):
            sql_query += " AND aj.deposit_type = %s"
            query_params.append(config['deposit_type_filter'])

            # Add bank name filter if specified
            if config.get('bank_name'):
                sql_query += " AND rb.name = %s"
                query_params.append(config['bank_name'])

        sql_query += " ORDER BY td.open_date ASC"

        # Execute query
        request.env.cr.execute(sql_query, tuple(query_params))
        results = request.env.cr.dictfetchall()

        return results

    def _generate_multiple_tables_report(self, base_domain, business_unit_id, start_date, end_date):
        """Generate Excel report with 4 separate tables when all filters are 'all'"""

        # Get business unit name
        business_unit = request.env['res.company'].sudo().browse(business_unit_id)
        business_unit_name = business_unit.name if business_unit else ''

        # Prepare Excel file
        output = io.BytesIO()
        workbook = xlsxwriter.Workbook(output, {'in_memory': True})

        # Define formats
        title_format = workbook.add_format({
            'bold': True,
            'font_size': 16,
            'align': 'center',
            'valign': 'vcenter'
        })

        subtitle_format = workbook.add_format({
            'bold': True,
            'font_size': 14,
            'align': 'left',
            'valign': 'vcenter',
            'bg_color': '#E6E6FA'
        })

        header_format = workbook.add_format({
            'bold': True,
            'bg_color': '#D3D3D3',
            'border': 1,
            'align': 'center',
            'valign': 'vcenter',
            'text_wrap': True
        })

        cell_format = workbook.add_format({
            'border': 1,
            'align': 'left',
            'valign': 'vcenter'
        })

        number_format = workbook.add_format({
            'border': 1,
            'align': 'right',
            'valign': 'vcenter',
            'num_format': '#,##0.00'
        })

        # Create worksheet
        sheet = workbook.add_worksheet('Time Deposit Report')

        # Set column widths
        sheet.set_column('A:A', 5)   # No
        sheet.set_column('B:B', 15)  # Deposit Number
        sheet.set_column('C:C', 12)  # Open Date
        sheet.set_column('D:D', 12)  # Mature Date
        sheet.set_column('E:E', 12)  # Deposit In Days
        sheet.set_column('F:F', 15)  # Bank
        sheet.set_column('G:G', 15)  # Amount
        sheet.set_column('H:H', 12)  # Interest Rate
        sheet.set_column('I:I', 15)  # Beneficiary Bank
        sheet.set_column('J:J', 15)  # Bank Account
        sheet.set_column('K:K', 10)  # Type
        sheet.set_column('L:L', 15)  # No Bilyet Deposito
        sheet.set_column('M:M', 12)  # Classification
        sheet.set_column('N:N', 15)  # Remarks
        sheet.set_column('O:O', 12)  # Breakable %
        sheet.set_column('P:P', 15)  # Deposit Status

        # Write main title
        sheet.merge_range('A1:P1', 'List Time Deposit Report', title_format)

        # Write filter information
        row = 2
        sheet.write(row, 0, f'Business Unit: {business_unit_name}', cell_format)
        row += 1
        sheet.write(row, 0, f'Start Date: {start_date}', cell_format)
        row += 1
        sheet.write(row, 0, f'End Date: {end_date}', cell_format)
        row += 1
        sheet.write(row, 0, 'Product: All', cell_format)
        row += 1
        sheet.write(row, 0, 'Deposit Type: All', cell_format)

        # Skip a row
        row += 2

        # Define table configurations using dynamic filtering
        table_configs = self._get_dynamic_table_configs()

        # Generate each table
        for config in table_configs:
            row = self._write_table_section(
                sheet, config, business_unit_id, start_date, end_date, row,
                subtitle_format, header_format, cell_format, number_format
            )
            row += 2  # Add space between tables

        # Add total row using raw SQL query
        row = self._write_total_section(sheet, workbook, row, business_unit_id, start_date, end_date)

        # Close workbook and prepare response
        workbook.close()
        output.seek(0)

        # Generate filename with timestamp
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'time_deposit_report_all_{timestamp}.xlsx'

        # Create response
        response = request.make_response(
            output.read(),
            headers=[
                ('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'),
                ('Content-Disposition', content_disposition(filename))
            ]
        )

        return response

    def _get_dynamic_table_configs(self):
        """Get dynamic table configurations based on journal deposit_type using raw SQL"""
        table_configs = []

        # Get unique combinations of journal deposit_type using raw SQL
        sql_query = """
            SELECT DISTINCT
                aj.deposit_type as journal_deposit_type,
                rb.name as bank_name,
                COUNT(td.id) as deposit_count
            FROM time_deposit td
            LEFT JOIN account_journal aj ON td.deposit_partner_bank_id = aj.id
            LEFT JOIN res_bank rb ON aj.bank_id = rb.id
            WHERE td.state != 'extend'
            AND td.deposit_partner_bank_id IS NOT NULL
            AND aj.deposit_type IS NOT NULL
            GROUP BY aj.deposit_type, rb.name
            ORDER BY aj.deposit_type, rb.name
        """

        request.env.cr.execute(sql_query)
        results = request.env.cr.dictfetchall()

        # Group by journal deposit_type
        journal_type_combinations = {}
        for result in results:
            journal_deposit_type = result['journal_deposit_type']
            bank_name = result['bank_name'] or 'Unknown Bank'
            deposit_count = result['deposit_count']

            if journal_deposit_type:
                key = f"{journal_deposit_type}_{bank_name}"
                if key not in journal_type_combinations:
                    journal_type_combinations[key] = {
                        'journal_deposit_type': journal_deposit_type,
                        'bank_name': bank_name,
                        'deposit_count': deposit_count
                    }

        # Create table configs based on journal deposit types
        for combo in journal_type_combinations.values():
            journal_type = combo['journal_deposit_type']
            bank_name = combo['bank_name']

            if journal_type == 'konven':
                # Check if this is a merchant account based on bank name
                if 'merchant' in bank_name.lower() or 'bri' in bank_name.lower():
                    table_configs.append({
                        'title': f'Deposito Bank Konvensional Merchant - {bank_name}',
                        'deposit_type_filter': 'konven',
                        'bank_name': bank_name
                    })
                else:
                    table_configs.append({
                        'title': f'Deposito Bank Konvensional IDR - {bank_name}',
                        'deposit_type_filter': 'konven',
                        'bank_name': bank_name
                    })
            elif journal_type == 'syariah':
                table_configs.append({
                    'title': f'Deposito Bank Syariah - {bank_name}',
                    'deposit_type_filter': 'syariah',
                    'bank_name': bank_name
                })

        # If no specific combinations found, add generic configs
        if not table_configs:
            table_configs = [
                {
                    'title': 'Deposito Bank Konvensional IDR',
                    'deposit_type_filter': 'konven'
                },
                {
                    'title': 'Deposito Bank Syariah',
                    'deposit_type_filter': 'syariah'
                }
            ]

        # Add USD table (empty for now)
        table_configs.append({
            'title': 'Deposito USD',
            'deposit_type_filter': 'usd'
        })

        return table_configs

    def _write_table_section(self, sheet, config, business_unit_id, start_date, end_date,
                           start_row, subtitle_format, header_format, cell_format, number_format):
        """Write a table section for specific category using raw SQL"""

        # Get data for this table using raw SQL
        time_deposits = self._get_table_data_raw(
            config, business_unit_id, start_date, end_date
        )

        # Write table title
        sheet.merge_range(f'A{start_row + 1}:P{start_row + 1}', config['title'], subtitle_format)
        row = start_row + 2

        # Write headers
        headers = [
            'No', 'Deposit Number', 'Open Date', 'Mature Date', 'Deposit In Days',
            'Bank', 'Amount', 'Interest Rate', 'Beneficiary Bank', 'Bank Account',
            'Type', 'No Bilyet Deposito', 'Classification', 'Remarks', 'Breakable %',
            'Deposit Status'
        ]

        for col, header in enumerate(headers):
            sheet.write(row, col, header, header_format)

        # Write data
        row += 1
        if time_deposits:
            deposit_type_dict = {
                'ccredit': 'Corporate Credit Card',
                'doc': 'DOC',
                'deposit': 'Deposit',
                'mma': 'MMA',
                'optima': 'Optima'
            }

            for idx, deposit in enumerate(time_deposits, 1):
                sheet.write(row, 0, idx, cell_format)
                sheet.write(row, 1, deposit.get('name', ''), cell_format)

                # Open Date
                if deposit.get('open_date'):
                    open_date = deposit['open_date']
                    if hasattr(open_date, 'strftime'):
                        sheet.write(row, 2, open_date.strftime('%d-%m-%Y'), cell_format)
                    else:
                        sheet.write(row, 2, str(open_date), cell_format)
                else:
                    sheet.write(row, 2, '', cell_format)

                # Mature Date
                if deposit.get('maturity_date'):
                    maturity_date = deposit['maturity_date']
                    if hasattr(maturity_date, 'strftime'):
                        sheet.write(row, 3, maturity_date.strftime('%d-%m-%Y'), cell_format)
                    else:
                        sheet.write(row, 3, str(maturity_date), cell_format)
                else:
                    sheet.write(row, 3, '', cell_format)

                sheet.write(row, 4, deposit.get('deposit_in_days_placement', 0), cell_format)
                sheet.write(row, 5, self._format_bank_name(deposit.get('bank_name', '')), cell_format)
                sheet.write(row, 6, deposit.get('deposit_original_amount', 0), number_format)
                sheet.write(row, 7, deposit.get('interest_rate', 0), number_format)

                # Beneficiary Bank
                beneficiary_bank = self._format_bank_name(deposit.get('beneficiary_bank_name', ''))
                sheet.write(row, 8, beneficiary_bank, cell_format)
                sheet.write(row, 9, deposit.get('bank_account', ''), cell_format)

                # Type (deposit_type)
                sheet.write(row, 10, deposit_type_dict.get(deposit.get('deposit_type'), ''), cell_format)

                sheet.write(row, 11, '', cell_format)  # No Bilyet Deposito - empty for now
                sheet.write(row, 12, deposit.get('deposit_clasification', ''), cell_format)
                sheet.write(row, 13, deposit.get('deposit_note', ''), cell_format)
                sheet.write(row, 14, '', cell_format)  # Breakable % - empty for now

                # Deposit Status (state)
                state_dict = {
                    'draft': 'Draft',
                    'placement': 'Placement',
                    'extend': 'Extend',
                    'withdraw': 'Withdraw',
                    'cancel': 'Cancel'
                }
                sheet.write(row, 15, state_dict.get(deposit.get('state'), ''), cell_format)

                row += 1
        else:
            # Write "No data" message
            sheet.merge_range(f'A{row + 1}:P{row + 1}', 'No data available', cell_format)
            row += 1

        return row

    def _write_total_section(self, sheet, workbook, start_row, business_unit_id,
                           start_date, end_date):
        """Write total section using raw SQL query"""

        # Build SQL query to calculate total amount with dynamic date filtering
        sql_query = """
            SELECT COALESCE(SUM(deposit_original_amount), 0) as total_amount
            FROM time_deposit
            WHERE company_id = %s
            AND state != 'extend'
        """

        query_params = [business_unit_id]

        # Add date filters if provided
        if start_date:
            sql_query += " AND open_date >= %s"
            query_params.append(start_date)
        if end_date:
            sql_query += " AND open_date <= %s"
            query_params.append(end_date)

        # Execute query
        try:
            request.env.cr.execute(sql_query, tuple(query_params))
            result = request.env.cr.fetchone()
            total_amount = result[0] if result else 0

            # Debug: Log the result
            _logger = logging.getLogger(__name__)
            _logger.info(f"Total amount calculated: {total_amount} for company "
                        f"{business_unit_id}, date range {start_date} to {end_date}")

        except Exception as e:
            _logger = logging.getLogger(__name__)
            _logger.error(f"Error calculating total: {e}")
            total_amount = 0

        # Create special format for total row
        total_label_format = workbook.add_format({
            'bold': True,
            'border': 1,
            'align': 'left',
            'valign': 'vcenter',
            'bg_color': '#E6E6FA'
        })

        total_colon_format = workbook.add_format({
            'bold': True,
            'border': 1,
            'align': 'center',
            'valign': 'vcenter',
            'bg_color': '#E6E6FA'
        })

        total_amount_format = workbook.add_format({
            'bold': True,
            'border': 1,
            'align': 'right',
            'valign': 'vcenter',
            'num_format': '#,##0.00',
            'bg_color': '#E6E6FA'
        })

        # Write total row
        row = start_row + 2
        sheet.write(row, 0, 'Total Time Deposit', total_label_format)
        sheet.write(row, 1, ':', total_colon_format)
        sheet.write(row, 2, total_amount, total_amount_format)

        return row + 1

    def _format_bank_name(self, bank_name):
        """Format bank name by removing 'Bank' prefix and replacing full names with abbreviations"""
        if not bank_name:
            return ''
        # Replace specific bank names with their abbreviations
        bank_mappings = {
            'Bank Rakyat Indonesia': 'BRI',
        }
        # Try exact match first
        if bank_name in bank_mappings:
            return bank_mappings[bank_name]
        # Remove 'Bank' prefix if present
        if bank_name.startswith('Bank '):
            return bank_name[5:].strip()
        return bank_name
