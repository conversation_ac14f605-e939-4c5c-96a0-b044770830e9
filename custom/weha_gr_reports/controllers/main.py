from odoo import http
from odoo.http import request, content_disposition
import io
import xlsxwriter
from datetime import datetime
import html


import logging
_logger = logging.getLogger(__name__)



class WehaGrReport(http.Controller):

    @http.route('/gr_report/excel', type='http', auth='user', csrf=False)
    def generate_gr_report_excel(self, stock_gr_id=None):
            
        # Convert dates
        stock_picking_id = request.env['stock.picking'].sudo().search([('id', '=', stock_gr_id)], limit=1)

        # Prepare Excel file
        output = io.BytesIO()
        workbook = xlsxwriter.Workbook(output, {'in_memory': True})
        sheet = workbook.add_worksheet("GR Report")

        f1=workbook.add_format({'bold':True, 'border':2, 'border_color':'black'})
        f2=workbook.add_format({'border':1, 'border_color':'black'})
        f22=workbook.add_format({'border':1, 'border_color':'black', 'num_format': '#,##0.00'})
        f3=workbook.add_format({'bold':True})
        f33=workbook.add_format({'bold':True, 'num_format': '#,##0.00'})
        f4=workbook.add_format({'bold':True, 'font_size':14})
        
        total = sum(stock_picking_id.move_ids_without_package.mapped('ammount_currency'))
        
        sheet.set_column('A:O', 20)
        sheet.write(1, 0, "GR TOTAL AMOUNT : ", f3)
        sheet.write(1, 1, total, f33)

        # Header
        headers = ['PO Number', 'Vendor Name', 'Buyer', 'Requester', 'APPROVER', 'PO STATUS', 'GR Number', 'GR Date', 'Invoice Date', 
        'Invoiced Amount', 'Invoice Exchange Rate', 'Invoice Rate Type', 'Invoiced Amount(IDR)', 'Uninvoiced GR', 'Is Asset']
        for col, header in enumerate(headers):
            sheet.write(3, col, header, f1)

        row = 4
        for rec in stock_picking_id:
            sheet.write(row, 0, rec.purchase_dua_id.name, f2)
            sheet.write(row, 1, rec.partner_vendor_id.name, f2)
            sheet.write(row, 2, rec.buyer_employee_id.name, f2)
            sheet.write(row, 3, rec.resquester_user_id.name, f2)
            sheet.write(row, 4, rec.hierarchy_id.name, f2)
            sheet.write(row, 5, rec.purchase_dua_id.new_state, f2)
            sheet.write(row, 6, rec.name, f2)
            sheet.write(row, 7, str(rec.transaction_date), f2)
            sheet.write(row, 8, str(rec.invoice_date), f2)
            sheet.write(row, 9, total, f22)
            sheet.write(row, 10, '', f2)
            sheet.write(row, 11, '', f2)
            sheet.write(row, 12, total, f22)
            sheet.write(row, 13, '', f2)
            sheet.write(row, 14, '', f2)
            row += 1

        workbook.close()
        output.seek(0)

        filename = f"GR_Report.xlsx"


        return request.make_response(
            output.read(),
            headers=[
                ('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'),
                ('Content-Disposition', content_disposition(filename))
            ]
        )
