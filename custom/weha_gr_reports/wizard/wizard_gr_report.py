from odoo import models, fields, api
from odoo.exceptions import UserError, ValidationError


class WehaGrReportWizard(models.TransientModel):
    _name = 'stock.gr.wizard'
    _description = 'Wizard GR Report'


    stock_gr_id = fields.Many2one(
        string='GR Number',
        comodel_name='stock.picking',
        ondelete='cascade',
        domain=[('state_fppr','=', 'ap_invoice')]
        
    )


    def action_export_excel(self):

        return {
            'type': 'ir.actions.act_url',
            'url': f"/gr_report/excel?stock_gr_id={self.stock_gr_id.id}",
            'target': 'self',
            'close_on_report_download': True
        }
    