# -*- coding: utf-8 -*-

from odoo import http
from odoo.http import content_disposition, request


class AccountReportController(http.Controller):
    @http.route(
        '/xlsx_report/<string:model>/<int:id>/<string:name>', type='http', auth='user'
    )
    def get_xlsx_report(self, model, id, name, **kwargs):
        record = request.env[model].with_user(request.session.uid).browse(id)
        response = request.make_response(
            None,
            headers=[
                ('Content-Type', 'application/vnd.ms-excel'),
                ('Content-Disposition', content_disposition(f'{name}.xlsx')),
            ],
        )
        record._generate_xlsx(response)
        return response
