# -*- coding: utf-8 -*-
import logging

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError

_logger = logging.getLogger(__name__)


class AccountMoveReturnWizard(models.TransientModel):
    _name = 'account.move.return.wizard'
    _description = _('AccountMoveReturnWizard')

    name = fields.Char(_('Return note'))
    move_id = fields.Many2one('account.move', string='Move', required=True)
    back_state = fields.Selection([
        ('draft', 'Draft'),
        ('verification', 'Tax Verification'),
        ('validate', 'Validate'),
        ('pending', 'Pending Approval'),
        ('approve', 'Approve'),
        ('return', 'Return'),
        ('rejected', 'Rejected'),
    ], string='Back State')
    

    def action_return(self):
        self.move_id.return_note = self.name
        self.move_id.validation_state = 'draft'
        if self.move_id.move_type == 'in_invoice':
            if self.move_id.state != 'draft':
                self.move_id.button_draft()
            self.move_id.picking_id.action_button_return_to_draft()
        if self.picking_journal_bill_id:
            self.picking_journal_bill_id.action_button_return_to_draft()
