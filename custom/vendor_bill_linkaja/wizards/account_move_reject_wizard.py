# -*- coding: utf-8 -*-
import logging

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError

_logger = logging.getLogger(__name__)


class AccountMoveRejectWizard(models.TransientModel):
    _name = 'account.move.reject.wizard'
    _description = _('AccountMoveRejectWizard')

    name = fields.Char(_('Reject Note'))
    move_id = fields.Many2one('account.move', string='Move', required=True)

    def action_reject(self):
        self.move_id.reject_note = self.name
        self.move_id.validation_state = 'rejected'
        if self.move_id.move_type == 'in_invoice' and self.move_id.state != 'cancel':
            self.move_id.button_cancel()
            self.move_id.validation_state = 'rejected'
