<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record model="ir.ui.view" id="view_move_form_ext">
            <field name="name">Account Move: Form View Ext</field>
            <field name="model">account.move</field>
            <field name="inherit_id" ref="account.view_move_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='invoice_line_ids']/list/field[@name='price_unit']" position="attributes">
                    <attribute name="column_invisible">parent.move_type == 'in_invoice'</attribute>
                </xpath>
                <xpath expr="//field[@name='invoice_line_ids']/list/field[@name='price_unit']" position="after">
                    <field name="new_unit_price" column_invisible="parent.move_type != 'in_invoice'" string="Unit Price"/>
                </xpath>

                <xpath expr="//div/h1[1]" position="attributes">
                    <attribute name="invisible">move_type == 'in_invoice'</attribute>
                </xpath>

                <xpath expr="//div/h1[1]" position="after">
                    <h1>
                        <field name="name" invisible="move_type != 'in_invoice'" readonly="1" force_save='1' placeholder="Draft"/>
                    </h1>
                </xpath>

                <!-- Customer Invoices -->
                <xpath expr="//button[@name='action_post'][2]" position="after">
                    <!-- <button string="Submit" name="send_message_email_tax" type="object" class="oe_highlight" invisible="state != 'draft' or move_type != 'out_invoice'" /> -->
                </xpath>

                <!-- <xpath expr="//button[@name='action_post'][1]" position="attributes">
                    <attribute name="invisible">state != 'tax'</attribute>
                </xpath> -->

                <xpath expr="//field[@name='state'][1]" position="replace">
                    <!-- <field name="state" widget="statusbar" statusbar_visible="draft,tax,posted" groups="customer_ext_linkaja.group_user_tax" invisible="move_type != 'out_invoice'"/> -->
                    <field name="state" widget="statusbar" statusbar_visible="draft,posted" />
                </xpath>

                <!-- Hide default state and show new state on VB -->
                <xpath expr="//field[@widget='statusbar']" position="attributes">
                    <attribute name="invisible">move_type == 'in_invoice'</attribute>
                </xpath>
                <xpath expr="//field[@widget='statusbar']" position="after">
                    <field name="validation_state" widget="statusbar" invisible="move_type != 'in_invoice'" groups="!account.group_account_secured" statusbar_visible="draft,submit,verification,validate,initiate_approval,pending,approve"/>
                </xpath>

                <xpath expr="//div[@name='journal_div']" position="attributes">
                    <attribute name="invisible">True</attribute>
                </xpath>

                <xpath expr="//div[@name='journal_div']" position="after">
                    <div name="journal_div_inh" class="d-flex" groups="account.group_account_readonly,base.group_multi_currency">
                        <field name="journal_id" groups="account.group_account_readonly" options="{'no_create': True, 'no_open': True}" readonly="state != 'draft' or validation_state not in ('draft', 'submit') or picking_journal_bill_id"/>
                    </div>
                </xpath>

                <!-- Adding validation button -->
                <xpath expr="//button[@name='action_post'][2]" position="attributes">
                    <attribute name="invisible">move_type in ['entry','in_invoice'] or (move_type == 'out_invoice' and state != 'draft')</attribute>
                    <!-- <attribute name="groups">!customer_ext_linkaja.group_user_tax</attribute> -->
                </xpath>
                <!-- <xpath expr="//button[@name='action_post'][2]" position="after">
                    <button name="action_post" string="Confirm" class="oe_highlight" type="object" groups="customer_ext_linkaja.group_user_tax" data-hotkey="q" context="{'validate_analytic': True, 'disable_abnormal_invoice_detection': False}" invisible="hide_post_button or move_type == 'entry' or display_inactive_currency_warning or state != 'tax'"/>
                </xpath> -->
                <xpath expr="(//button[@name='action_post'])[last()]" position="after">
                    <field name="tax_verification_type" invisible="1" />
                    <button name="action_set_to_draft" class="oe_highlight" type="object" string="Set To Draft" invisible="move_type != 'in_invoice' or validation_state == 'draft'"/>
                    <button name="button_draft_invoice" class="oe_highlight" type="object" string="Set To Draft" invisible="move_type != 'out_invoice' or state != 'posted'"/>
                    <button name="action_submit" class="oe_highlight" type="object" string="Submit" invisible="move_type != 'in_invoice' or validation_state != 'draft'"/>
                    <button name="action_verification" class="oe_highlight" type="object" string="Tax Verification" invisible="move_type != 'in_invoice' or validation_state != 'submit'"/>
                    <button name="action_validate" class="oe_highlight" type="object" string="Validate" invisible="move_type != 'in_invoice' or validation_state != 'verification'"/>
                    <button name="action_return_to_submitter" type="object" string="Return to Submitter" invisible="move_type != 'in_invoice' or validation_state not in ('validate', 'initiate_approval')"/>
                    <button name="action_return_to_tax" type="object" string="Return to Tax" invisible="move_type != 'in_invoice' or validation_state not in ('validate', 'initiate_approval')"/>
                    <button name="action_initiate_approval" class="oe_highlight" type="object" string="Initiate" invisible="move_type != 'in_invoice' or validation_state != 'validate'"/>
                    <button name="action_initiate" class="oe_highlight" type="object" string="Initiate Approval" invisible="move_type != 'in_invoice' or validation_state != 'initiate_approval'"/>
                    <button name="action_approve" class="oe_highlight" type="object" string="Approve" invisible="move_type != 'in_invoice' or validation_state != 'pending'"/>
                    <button name="action_reject" type="object" string="Reject" invisible="move_type != 'in_invoice' or validation_state not in ('approve', 'draft', 'submit')"/>
                    <!-- <button name="action_withdraw_approval" type="object" string="Withdraw Approval" invisible="move_type != 'in_invoice' or validation_state != 'pending'"/> -->
                </xpath>


                <xpath expr="//button[@name='button_cancel'][1]" position="replace">
                    <button name="button_cancel" type="object" string="Cancel" invisible="move_type != 'in_invoice' or validation_state not in ('approve', 'draft', 'submit')"/>
                </xpath>

                <xpath expr="//button[@name='button_cancel'][2]" position="replace">
                    <button name="button_cancel" type="object" string="Cancel" class="d-none"/>
                </xpath>
                <!-- Manage vendor bill body -->
                <!-- <xpath expr="//group[@id='header_left_group']" position="attributes">
                    <attribute name="invisible">move_type == 'in_invoice'</attribute>
                </xpath>
                <xpath expr="//group[@id='header_right_group']" position="attributes">
                    <attribute name="invisible">move_type == 'in_invoice'</attribute>
                </xpath> -->

                <xpath expr="//field[@name='fpjp_id']" position="replace"/>
                    

                <xpath expr="//field[@name='partner_id']" position="replace">
                    <field name="domain_partner_ids" invisible="1"/>
                    <field name="partner_id" widget="res_partner_many2one" nolabel="1"
                                           context="{
                                            'res_partner_search_mode': (context.get('default_move_type', 'entry') in ('out_invoice', 'out_refund', 'out_receipt') and 'customer') or (context.get('default_move_type', 'entry') in ('in_invoice', 'in_refund', 'in_receipt') and 'supplier') or False,
                                            'default_is_company': True}"
                                           domain="[('id', 'in', domain_partner_ids)]"
                                           options='{"no_quick_create": True}'
                                           required="move_type == 'in_invoice'"
                                           placeholder="Search a name or Tax ID..."
                                           force_save="1"
                                           readonly="(is_fpjp_bill and move_type == 'in_invoice') or state != 'draft' or validation_state not in ('draft', 'submit') or picking_journal_bill_id"/>
                </xpath>
                <xpath expr="//field[@name='partner_shipping_id']" position="after">
                    <field name="is_fpjp_bill" invisible="1" />
                    <field name="vendor_contact_id" force_save="1" required="move_type == 'in_invoice'"  invisible="move_type != 'in_invoice'" context="{'hide_company_name':1,'default_parent_id':partner_id}" readonly="is_fpjp_bill or source == 'purchase' or state != 'draft' or validation_state not in ('draft', 'submit') or picking_journal_bill_id"/> 
                    <field name="contact_email"  invisible="move_type != 'in_invoice'" readonly="1"/>
                    <field name="address" invisible="move_type != 'in_invoice'" />
                    <field name="source"  invisible="move_type != 'in_invoice'" readonly="1"/>
                    <field name="purchase_order_id"  invisible="move_type != 'in_invoice' or source != 'purchase'" readonly="1" domain="[('state','in',('purchase','approve'))]"/>
                    <field name="picking_id"  invisible="move_type != 'in_invoice' or source != 'purchase'" force_save="1" readonly="state != 'draft' or validation_state not in ('draft', 'submit') or picking_journal_bill_id"/>
                    <field name="fpjp_id"  invisible="move_type != 'in_invoice' or source != 'fpjp'" force_save="1" readonly="state != 'draft' or validation_state not in ('draft', 'submit') or picking_journal_bill_id"/>
                    <field name="fpjp_source" invisible="1"/>
                    <field name="payment_reference"  invisible="move_type != 'in_invoice'" force_save="1" readonly="state != 'draft' or validation_state not in ('draft', 'submit') or picking_journal_bill_id"/>
                    <field name="partner_bank_id" required="move_type in ('in_invoice', 'in_refund', 'in_receipt')" context="{'default_partner_id': bank_partner_id, 'display_account_trust': True}" force_save="1" domain="[('partner_id', '=', bank_partner_id)]" invisible="move_type not in ('in_invoice', 'in_refund', 'in_receipt')" readonly="state != 'draft' or validation_state not in ('draft', 'submit') or picking_journal_bill_id"/>
                    <field name="submitter_id" invisible="move_type != 'in_invoice'"  readonly="1" force_save="1"/>
                    <field name="return_note"  invisible="move_type != 'in_invoice'" readonly="1" force_save="1"/>
                    <field name="reject_note"  invisible="move_type != 'in_invoice'" readonly="1" force_save="1"/>
                </xpath>

                <xpath expr="//field[@name='delivery_date']" position="after">
                        <field name="requestor_id" required="1" invisible="move_type != 'in_invoice'" force_save="1" readonly="state != 'draft' or validation_state not in ('draft', 'submit') or is_fpjp_bill or picking_journal_bill_id"/>
                        <field name="unit_id" invisible="move_type != 'in_invoice'" context="{'hierarchical_naming':False}" readonly="1" force_save="1"/>
                        <field name="group_department_id" invisible="move_type != 'in_invoice'" context="{'hierarchical_naming':False}" readonly="1" force_save="1"/>
                        <field name="budgetary_position_id" force_save="1" required="move_type == 'in_invoice' and ((fpjp_source != 'non_justification' and fpjp_source != 'manual') or (fpjp_source == 'manual' and fpjp_source_transaction == 'justification'))" invisible="move_type != 'in_invoice'" readonly="state != 'draft' or validation_state not in ('draft', 'submit') or is_fpjp_bill or picking_journal_bill_id"/>
                        <field name="rkap_code" invisible="move_type != 'in_invoice'" readonly="1" force_save="1"/>
                        <field name="rkap_category_id" invisible="move_type != 'in_invoice'" readonly="1" force_save="1"/>
                        <field name="rkap_type_id" invisible="move_type != 'in_invoice'" readonly="1" force_save="1"/>
                        <field name="pay_group" force_save="1" required="move_type == 'in_invoice'" invisible="move_type != 'in_invoice'" readonly="state != 'draft' or validation_state != 'draft'"/>
                        <field name="project_id" required="move_type == 'in_invoice' and is_project_readonly and validation_state == 'draft'" invisible="move_type != 'in_invoice'" force_save="1" readonly="state != 'draft' or validation_state not in ('draft', 'submit') or is_fpjp_bill or picking_journal_bill_id or (source == 'manual' and not is_project_readonly)"/>
                </xpath>

                <xpath expr="//div[@name='due_date']" position="replace">
                    <div class="d-flex" invisible="move_type not in ('out_invoice', 'out_refund', 'in_invoice', 'in_refund', 'out_receipt', 'in_receipt')" name="due_date">
                        <field name="invoice_date_due" force_save="1" placeholder="Date" readonly="state != 'draft' or validation_state not in ('draft', 'submit') or picking_journal_bill_id or invoice_payment_term_id" required="payment_state not in ('paid', 'in_payment', 'reversed') or state != 'cancel'"/>
                        <span class="o_form_label mx-3 oe_edit_only"> or </span>
                        <field name="invoice_payment_term_id" context="{'example_date': invoice_date, 'example_amount': tax_totals['total_amount_currency']}" placeholder="Payment Terms" options="{'no_quick_create':True}" readonly="state in ['cancel', 'posted']"/>
                    </div>
                </xpath>

                <xpath expr="//field[@name='invoice_line_ids']/list/field[@name='name']" position="attributes">
                    <attribute name="column_invisible">parent.move_type == 'in_invoice'</attribute>
                </xpath>
                <xpath expr="//field[@name='invoice_line_ids']/list/field[@name='name']" position="replace"/>

                <xpath expr="//field[@name='invoice_date'][2]" position="attributes">
                    <attribute name="required">1</attribute>
                                        
                </xpath>

                
                <xpath expr="//field[@name='invoice_line_ids']/list/field[@name='product_id']" position="replace">
                    <field name="product_template_id" column_invisible="parent.move_type not in ['in_invoice', 'entry', 'in_receipt'] or parent.is_fpjp_bill or parent.source == 'manual'" domain="product_tmpl_domain" />
                    <!-- <field name="product_template_id" required="parent.move_type == 'in_invoice'"/> -->
                    <field name="product_id" string="Product Variant" domain="product_domain" column_invisible="parent.move_type != 'in_invoice' or parent.is_fpjp_bill or parent.source == 'manual'"/>
                    <field name="product_id" string="Product" optional="show" widget="product_label_section_and_note_field" domain="
                                                    context.get('default_move_type') in ('out_invoice', 'out_refund', 'out_receipt')
                                                    and [('sale_ok', '=', True)]
                                                    or [('purchase_ok', '=', True)]"
                                                column_invisible="parent.move_type == 'in_invoice'"
                                               />
                    <field name="name" column_invisible="1" />
                    <field name="type" column_invisible="parent.source != 'fpjp'"/>
                    <field name="product_category_id" column_invisible="parent.move_type != 'in_invoice' or parent.is_fpjp_bill or parent.source == 'manual'"/>
                    <field name="description" />
                    <field name="account_id" optional="show" column_invisible="parent.source != 'purchase'" domain="[('account_type', 'in', ('liability_non_current', 'liability_current', 'liability_credit_card', 'liability_payable', 'expense', 'expense_depreciation', 'expense_direct_cost'))]"/>
                    <field name="category_id" required="move_type == 'in_invoice' and parent.source == 'fpjp'" column_invisible="parent.move_type != 'in_invoice' or parent.source != 'fpjp'"/>
                    <field name="currency_conversion_id" force_save='1' column_invisible="parent.move_type != 'in_invoice'"/>
                    <field name="manual_conversion" force_save='1' readonly="parent.source != 'manual' or currency_conversion_id == company_currency_id" column_invisible="parent.move_type != 'in_invoice'"/>
                    <!-- <field name="manual_conversion_rate" column_invisible="parent.move_type != 'in_invoice'" invisible="0"/> -->
                    <field name="inverse_conv_rate" force_save='1' column_invisible="parent.move_type != 'in_invoice'" readonly="manual_conversion == False or parent.is_fpjp_bill"/>
                </xpath>

                <!-- <xpath expr="//field[@name='invoice_line_ids']/list/field[@name='account_id']" position="attributes">
                    <attribute name="optional">hide</attribute>
                    <attribute name="domain">[('account_type', 'in', ('liability_non_current', 'liability_current', 'liability_credit_card', 'liability_payable', 'expense', 'expense_depreciation', 'expense_direct_cost'))]</attribute>
                </xpath> -->

                <!--<xpath expr="//field[@name='invoice_line_ids']/list/field[@name='account_id']" position="attributes">
                    <attribute name="optional">show</attribute>
                    <attribute name="column_invisible">parent.move_type  == 'in_invoice'</attribute>
                </xpath>-->

                <xpath expr="//field[@name='invoice_line_ids']/list/field[@name='account_id']" position="after">
                    <field name="account_id" optional="hide" column_invisible="parent.move_type  != 'in_invoice'" invisible="display_type in ('line_section', 'line_note')" required="display_type not in ('line_section', 'line_note')" domain="[('account_type', 'in', ('liability_non_current', 'liability_current', 'liability_credit_card', 'liability_payable', 'expense', 'expense_depreciation', 'expense_direct_cost'))]"/>
                </xpath>

                <xpath expr="//field[@name='invoice_line_ids']/list/field[@name='analytic_distribution']" position="attributes">
                    <attribute name="optional">hide</attribute>
                </xpath>
                <xpath expr="//group[@id='header_right_group']/field[@name='payment_reference']" position="attributes">
                    <!-- <attribute name="invisible">move_type not in ('in_refund', 'out_receipt', 'in_receipt')</attribute> -->
                    <attribute name="invisible">1</attribute>
                    
                </xpath>
                <xpath expr="//group[@id='header_right_group']/field[@name='partner_bank_id']" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>
                <xpath expr="//group[@id='header_right_group']/field[@name='invoice_date'][2]" position="attributes">
                    <attribute name="readonly">state != 'draft' or validation_state not in ('draft', 'submit') or picking_journal_bill_id</attribute>
                    <attribute name="force_save">True</attribute>
                </xpath>
                <xpath expr="//group[@id='header_right_group']/field[@name='date']" position="attributes">
                    <attribute name="readonly">state != 'draft' or validation_state not in ('draft', 'submit') or picking_journal_bill_id</attribute>
                    <attribute name="force_save">True</attribute>
                </xpath>
                <xpath expr="//group[@id='header_right_group']/div[2]/field[@name='invoice_payment_term_id']" position="attributes">
                    <attribute name="readonly">state in ['cancel', 'posted'] or validation_state not in ('draft', 'submit') or picking_journal_bill_id</attribute>
                    <attribute name="force_save">True</attribute>
                </xpath>
                <!-- <xpath expr="//group[@id='header_right_group']/div[@name='journal_div']/field[@name='journal_id']" position="attributes">
                    <attribute name="readonly">state != 'draft' or validation_state not in ('draft', 'submit') or picking_journal_bill_id</attribute>
                    <attribute name="force_save">True</attribute>
                </xpath> -->
                <xpath expr="//group[@id='header_right_group']/div[@name='journal_div']/div[@name='currency_div']/field[@name='currency_id']" position="attributes">
                    <attribute name="readonly">state != 'draft' or validation_state not in ('draft', 'submit') or picking_journal_bill_id</attribute>
                    <attribute name="force_save">True</attribute>
                </xpath>

                <xpath expr="//field[@name='invoice_line_ids']/list/field[@name='price_subtotal']" position="attributes">
                    <attribute name="column_invisible">parent.move_type == 'in_invoice'</attribute>
                </xpath>
                <xpath expr="//field[@name='invoice_line_ids']/list/field[@name='price_unit']" position="attributes">
                    <attribute name="string">Unit Price</attribute>
                    <!-- <attribute name="required">parent.move_type == 'in_invoice'</attribute> -->
                </xpath>
                <xpath expr="//field[@name='invoice_line_ids']/list/field[@name='price_total']" position="attributes">
                    <attribute name="column_invisible">parent.move_type == 'in_invoice'</attribute>
                </xpath>
                <xpath expr="//field[@name='invoice_line_ids']/list/field[@name='tax_ids']" position="attributes">
                    <attribute name="column_invisible">parent.move_type == 'in_invoice'</attribute>
                </xpath>
                <xpath expr="//field[@name='invoice_line_ids']/list/field[@name='discount']" position="attributes">
                    <attribute name="column_invisible">parent.move_type == 'in_invoice'</attribute>
                </xpath>
                <xpath expr="//field[@name='invoice_line_ids']/list/field[@name='product_uom_id']" position="attributes">
                    <attribute name="required">parent.move_type == 'in_invoice'</attribute>
                    <attribute name="domain">product_uom_domain</attribute>
                    <!-- <attribute name="readonly">parent.is_fpjp_bill</attribute> -->
                </xpath>
                <xpath expr="//field[@name='invoice_line_ids']/list/field[@name='quantity']" position="attributes">
                    <attribute name="required">parent.move_type == 'in_invoice'</attribute>
                    <!-- <attribute name="readonly">parent.is_fpjp_bill</attribute> -->
                </xpath>
                <xpath expr="//field[@name='invoice_line_ids']/list/field[@name='price_total']" position="before">
                    <!-- <field name="price_total_currency" string="Sub Total Currency" column_invisible="parent.move_type != 'in_invoice'"/>
                    <field name="price_subtotal" column_invisible="parent.move_type != 'in_invoice'" string="Sub Total IDR"/> -->
                    <field name="price_total_currency_bill" options="{'currency_field': 'currency_conversion_id'}" column_invisible="parent.move_type != 'in_invoice'" string="Sub Total Currency"/>
                    <field name="price_subtotal_bill" string="Sub Total IDR " column_invisible="parent.move_type != 'in_invoice'"/>
                    <field name="tax_ids" widget="many2many_tax_tags" column_invisible="parent.move_type != 'in_invoice'" domain="[('type_tax_use', '=?', parent.invoice_filter_type_domain), ('company_id', 'parent_of', parent.company_id), ('country_id', '=', parent.tax_country_id)]" context="{'append_type_to_tax_name': not parent.invoice_filter_type_domain, 'active_test': True}" options="{'no_create': True}" optional="show"/>
                    <field name="discount" width="50px" string="Disc.%" optional="show" column_invisible="parent.move_type != 'in_invoice'"/>
                    <field name="amount_discount" column_invisible="parent.move_type != 'in_invoice'" string="Fixed Discount"/>
                </xpath>

                <xpath expr="//field[@name='invoice_line_ids']/list" position="attributes">
                    <attribute name="editable">bottom</attribute>
                </xpath>

                <xpath expr="//field[@name='invoice_line_ids']" position="attributes">
                    <attribute name="readonly">state != 'draft' or validation_state not in ('draft', 'submit')</attribute>
                    <attribute name="force_save">True</attribute>
                </xpath>

                <xpath expr="//field[@name='line_ids']" position="attributes">
                    <attribute name="readonly">state != 'draft' or validation_state not in ('draft', 'submit')</attribute>
                    <attribute name="force_save">True</attribute>
                </xpath>

                <xpath expr="//field[@name='line_ids']/list/field[@name='account_id']" position="attributes">
                    <attribute name="column_invisible">parent.move_type  == 'in_invoice'</attribute>
                </xpath>

                <xpath expr="//field[@name='line_ids']/list/field[@name='account_id']" position="after">
                    <field name="account_id" column_invisible="parent.move_type  != 'in_invoice'" invisible="display_type in ('line_section', 'line_note')" required="display_type not in ('line_section', 'line_note')" domain="[('account_type', 'in', ('liability_non_current', 'liability_current', 'liability_credit_card', 'liability_payable', 'expense', 'expense_depreciation', 'expense_direct_cost'))]"/>
                </xpath>

<!--                <xpath expr="//field[@name='invoice_line_ids']" position="attributes">-->
<!--                     <attribute name="delete">false</attribute>-->
<!--                </xpath>-->

                <xpath expr="//group[@class='oe_subtotal_footer']" position="attributes">
                    <attribute name="invisible">move_type not in ('out_refund', 'in_refund', 'out_receipt', 'in_receipt') or payment_state == 'invoicing_legacy'</attribute>
                </xpath>


                <xpath expr="//group[@class='oe_subtotal_footer']" position="before">
                    <table class="table table-sm" invisible="move_type != 'in_invoice'">
                        <tr>
                            <td class="text-right">Amount Discount:</td>
                            <td class="text-right">
                                <field name="amount_discount_total"  readonly="1"/>
                            </td>
                        </tr>
                        <tr>
                            <td class="text-right">Dasar Pengenaan Pajak:</td>
                            <td class="text-right">
                                <field name="dpp_amount"  readonly="1"/>
                            </td>
                        </tr>
                        <tr invisible="not has_dpp_nilai_lain">
                            <td class="text-right">DPP Nilai Lain:</td> 
                            <td class="text-right">
                                <field name="dpp_nilai_lain"  readonly="1"/>
                                <field name="has_dpp_nilai_lain" invisible='1' />
                            </td>
                        </tr>
                        <tr invisible="tax_amount &lt; 1">
                            <td class="text-right"><field name="label_ppn" force_save='1' readonly='1' widget="label"/></td>
                            <td class="text-right">
                                <field name="tax_amount" readonly="1"/>
                            </td>
                        </tr>
                        <tr invisible="tax_amount_pph == 0">
                            <td><field name="label_pph" force_save='1' readonly='1' widget="label"/></td>
                            <td class="text-right">
                                <field name="tax_amount_pph" readonly="1"/>
                            </td>
                        </tr>
                        <tr>
                            <td class="text-right">Total:</td>
                            <td class="text-right">
                                <field name="total_amount" readonly="1"/>
                            </td>
                        </tr>
                        <tr>
                            <td class="text-right">Residual Amount (IDR):</td>
                            <td class="text-right">
                                <field name="total_amount_residual" readonly="1"/>
                            </td>
                        </tr>      
                    </table>
                </xpath>

                <xpath expr="//field[@name='line_ids']/list/field[@name='analytic_distribution']" position="replace"/>
                <xpath expr="//field[@name='line_ids']/list/field[@name='name']" position="after">
                    <field name="description" string='Label' column_invisible="parent.move_type != 'in_invoice'"/>
                    <field name="group_id" column_invisible="parent.move_type not in ('in_invoice','in_receipt','entry')" context="{'hierarchical_naming': False}" />
                    <field name="rkap_code_id" column_invisible="parent.move_type != 'entry'"/>
                    <field name="rkap_code" column_invisible="parent.move_type not in ('in_invoice','in_receipt')"/>
                    <field name="analytic_distribution" string="Analytic Account" widget="analytic_distribution" groups="analytic.group_analytic_accounting" optional="show" options="{'account_field': 'account_id'}" business_domain_compute="parent.move_type in ['out_invoice', 'out_refund', 'out_receipt'] and 'invoice' or parent.move_type in ['in_invoice', 'in_refund', 'in_receipt'] and 'bill' or 'general'"/>
                    <field name="product_type" column_invisible="parent.move_type not in ('in_invoice','in_receipt')" />
                    <field name="currency_conversion_id" force_save='1' column_invisible="1"/>
                </xpath>

                <xpath expr="//field[@name='line_ids']/list/field[@name='name']" position="attributes">
                    <attribute name="column_invisible">parent.move_type == 'in_invoice'</attribute>
                </xpath>

                <!-- <xpath expr="//field[@name='tax_cash_basis_origin_move_id']" position="after">
                    <field name="group_id" invisible="move_type != 'entry'" readonly="1"/>
                    
                </xpath> -->

                <!-- <xpath expr="//field[@name='ref'][2]" position="attributes">
                    <attribute name="invisible">0</attribute>
                </xpath> -->
                <xpath expr="//field[@name='ref'][1]" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>
                <xpath expr="//label[@for='ref'][1]" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>
               


                <xpath expr="//notebook" position="inside">
                    <page string="Attachment" name="account_document">
                        <field name="account_document_line_ids" readonly="state != 'draft' or validation_state not in ('draft', 'submit')">
                            <list editable="bottom">
                                <field name="attachment" widget="binary" filename="filename" />
                                <field name="filename" column_invisible="1" readonly="1" force_save="1" />
                                <field name="name" required="1" />
                                <field name="internal_doc" />
                                <field name="supplier_doc" />
                            </list>
                        </field>
                    </page>
                    
                </xpath>

                <xpath expr="//field[@name='line_ids']/list/field[@name='debit']" position="before">
                    <field name="inverse_conv_rate" optional="hide" column_invisible="parent.move_type != 'in_invoice'"/>
                    <field name="price_total_currency_bill" optional="hide" column_invisible="parent.move_type != 'in_invoice'"/>
                </xpath>
            </field>
        </record>


        <record id="view_move_form_inherit_purchase_inherit_ext_vendor_bill" model="ir.ui.view">
            <field name="name">account.move.view.form.inherit.ext.vendor.bill</field>
            <field name="model">account.move</field>
            <field name="inherit_id" ref="purchase.view_move_form_inherit_purchase"/>
            <field name="arch" type="xml">

                 <xpath expr="//label[@for='purchase_vendor_bill_id']" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>
                <xpath expr="//field[@name='purchase_vendor_bill_id']" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>
                    
            </field>
        </record>

        <record id="currency_account_move_inherit_form_view_inherit_currency_manual_exchange_rate" model="ir.ui.view">
            <field name="name">account.move.view.form.inherit.currency_manual_exchange_rate</field>
            <field name="model">account.move</field>
            <field name="inherit_id" ref="currency_manual_exchange_rate.currency_account_move_inherit_form_view"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='manual_currency_rate_active']" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>
                
            </field>
        </record>


        <record id="account_move_efaktur_form_view_inherit_ext_linkaja" model="ir.ui.view">
            <field name="name">account.move.view.form.inherit</field>
            <field name="model">account.move</field>
            <field name="inherit_id" ref="l10n_id_efaktur.account_move_efaktur_form_view"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='l10n_id_tax_number']" position="replace">
                    <field name="l10n_id_tax_number" force_save="1" readonly="state != 'draft' or validation_state not in ('draft', 'submit') or picking_journal_bill_id" />
                    <field name="tax_account_code_id" force_save="1" readonly="state != 'draft' or validation_state not in ('draft', 'submit') or picking_journal_bill_id" />
                    <field name="deposit_type_code_id" force_save="1" readonly="state != 'draft' or validation_state not in ('draft', 'submit') or picking_journal_bill_id"/>
                    
                </xpath>
            </field>
        </record>

        <record id="view_in_invoice_bill_tree_inherit_vendor_bill_linkaja" model="ir.ui.view">
            <field name="name">account.move.view.list.inherit.vendor.bill.linkaja</field>
            <field name="model">account.move</field>
            <field name="inherit_id" ref="account.view_in_invoice_bill_tree"/>
            <field name="arch" type="xml">
                <!-- <xpath expr="//field[@name='status_in_payment']" position="after">
                    <field name="validation_state" widget="badge" invisible="move_type != 'in_invoice'"/>
                    
                    
                </xpath> -->


                <xpath expr="//field[@name='amount_untaxed_in_currency_signed']" position="attributes">
                    <attribute name='column_invisible'>1</attribute>
                </xpath>
                <xpath expr="//field[@name='amount_untaxed_in_currency_signed']" position="after">
                    <field name="dpp_amount" optional="show"/>
                </xpath>
                <xpath expr="//field[@name='amount_total_in_currency_signed'][1]" position="attributes">
                    <attribute name='column_invisible'>1</attribute>
                </xpath>
                <xpath expr="//field[@name='amount_total_in_currency_signed'][2]" position="attributes">
                    <attribute name='column_invisible'>1</attribute>
                </xpath>
                <xpath expr="//field[@name='amount_total_in_currency_signed']" position="after">
                    <field name="total_amount"/>
                </xpath>
                
            </field>
        </record>
    </data>
</odoo>