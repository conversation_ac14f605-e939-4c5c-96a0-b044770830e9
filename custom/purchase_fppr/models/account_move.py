from odoo import fields, api, models, _
from odoo.exceptions import ValidationError, UserError
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
from collections import defaultdict
from odoo.tools.translate import _


class AccountMoveInherit(models.Model):
    _inherit = 'account.move'

    def _prepare_product_base_line_for_taxes_computation(self, product_line):
        res = super(AccountMoveInherit, self)._prepare_product_base_line_for_taxes_computation(product_line)
        if product_line.stock_move_id:
            res['quantity'] = product_line.stock_move_id.ammount_currency / product_line.stock_move_id.purchase_order_line_id.price_unit
        return res

    def button_cancel(self):
        res = super().button_cancel()
        if self.picking_journal_bill_id:
            self.picking_journal_bill_id.action_cancel()
        return res


class AccountMoveLine(models.Model):
    _inherit = 'account.move.line'

    stock_move_id = fields.Many2one('stock.move', 'Picking Line')

    @api.depends('quantity', 'new_unit_price', 'price_total_currency_bill', 'stock_move_id')
    def _compute_price_total_cure(self):
        for record in self:
            if record.stock_move_id:
                record.price_total_currency_bill = record.stock_move_id.ammount_currency
            else:
                record.price_total_currency_bill = record.quantity * record.new_unit_price
