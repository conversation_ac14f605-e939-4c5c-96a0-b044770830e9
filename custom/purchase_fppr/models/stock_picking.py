from odoo import fields, api, models, _
from odoo.exceptions import ValidationError, UserError
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
from collections import defaultdict
from odoo.tools.translate import _
from odoo.tools.float_utils import float_compare, float_round


class StockPicking(models.Model):
    _inherit = 'stock.picking'
    _order = 'id desc'
    
    def default_currency_id(self):
        currency = self.env['res.currency'].search([('name', '=', 'IDR')], limit=1)
        if currency:
            return currency.id
        else:
            return False

    def _get_default_accounting_period(self):
        accounting_period = self.env['sh.account.period'].search([('date_start', '<=', datetime.now().date()), ('date_end', '>=', datetime.now().date())], limit=1)
        if accounting_period:
            return accounting_period
        else:
            return False

    state_fppr = fields.Selection([
        ('draft', 'Draft'),
        ('pending_approval', 'Pending Approval'),
        ('ap_invoice', 'AP Invoice'),
        ('return', 'Return'),
        ('cancel', 'Cancel'),
    ], string='State', default="draft")
    purchase_id = fields.Many2one('purchase.order', string='Source Document')
    purchase_dua_id = fields.Many2one('purchase.order', string='Purchase Dua', tracking=True)
    partner_vendor_id = fields.Many2one('res.partner', string='Vendor', ondelete='set null', tracking=True)
    user_id = fields.Many2one('res.users', string='Buyer', tracking=True)
    buyer_employee_id = fields.Many2one('hr.employee', string='Buyer', related='purchase_dua_id.buyer_id')
    resquester_user_id = fields.Many2one('res.users', string='Requested By', tracking=True)
    created_user_id = fields.Many2one('res.users', string='Created By', tracking=True)
    validity_date = fields.Date('Validity Date', tracking=True)
    transaction_date = fields.Date('Transaction Date', default=fields.Date.context_today, readonly=True, tracking=True, )
    accounting_date = fields.Date('Accounting Date', readonly=True, tracking=True, compute='_compute_transaction_date' )
    invoice_date = fields.Date('Invoice Date', tracking=True)
    nomor_invoice = fields.Char('Nomor Invoice', tracking=True)
    invoice_move_id = fields.Many2one('account.move', string='Nomor Invoice', tracking=True, domain="[('move_type', '=', 'out_invoice'), ('state', '=', 'posted')]")
    currency_id = fields.Many2one('res.currency', string='currency', ondelete='set null', default=default_currency_id, tracking=True)
    invoice_amount = fields.Monetary('Invoice Amount', compute='_compute_amount_currency', store=True, tracking=True)
    attachment_nodin = fields.Binary('Attachment Nodin')
    name_attachment_nodin = fields.Char('Name ATtechment Nodin', tracking=True)
    attachment_invoice = fields.Binary('Attachment Invoice')
    name_attachment_invoice = fields.Char('Name ATtachment Invoice')
    cip_id = fields.Many2one('phase.project.cip', string='Project', ondelete='set null', store=True, tracking=True)
    nilai_akhir = fields.Float('Nilai Akhir', compute="_compute_nilai_akhir", store=True, tracking=True)
    description = fields.Char('Description', compute='_compute_description')
    total_bobot = fields.Float(compute='_compute_total_bobot', string='Total Bobot', store=True, tracking=True)
    is_mandatory_attachment_nodin = fields.Boolean('Is MAndatory ATtachment Nodin', default=False, compute='_compute_mandatory_attachment')
    accounting_period = fields.Date('Accounting Period', tracking=True)
    accounting_period_id = fields.Many2one('sh.account.period', string='Accounting Period', tracking=True, default=_get_default_accounting_period)
    name = fields.Char(tracking=True)
    partner_id = fields.Many2one(tracking=True)
    picking_type_id = fields.Many2one(tracking=True)
    location_id = fields.Many2one(tracking=True)
    location_dest_id = fields.Many2one(tracking=True)


    @api.constrains('invoice_date', 'transaction_date')
    def _check_invoice_date(self):
        for record in self:
            if record.invoice_date and record.transaction_date:
                if record.transaction_date < record.invoice_date:
                    raise ValidationError("Transaction Date tidak boleh lebih kecil dari Invoice Date.")
    

    def _default_assesment_purchase_lines(self):
        aspect_lines = self.env['assesment.aspect.line'].search([('aspect_id.name', '=', 'fppr')])
        return [(0, 0, {'assesment_aspect': aspect.name, 'persentasi_bobot': aspect.weight}) for aspect in aspect_lines]
    assesment_purchase_line_ids = fields.One2many('assesment.purchase.line', 'picking_id', string='Assesment Purchase Line', default=lambda self: self._default_assesment_purchase_lines())

    stock_move_line_ids = fields.One2many('stock.move', 'picking_id', string='Stock Move Line')
    approval_gr_line_ids = fields.One2many('approval.gr.line', 'picking_id', string='Approval GR Line')

    picking_journal_gr_id = fields.Many2one(
        'account.move',
        string='Journal Entry',
        help='Linked journal entry created from this Good Receipt'
    )
    
    @api.model
    def default_get(self, fields_list):
        res = super(StockPicking, self).default_get(fields_list)

        picking_type_id = res.get('picking_type_id')
        picking_type = self.env['stock.picking.type'].browse(picking_type_id) if picking_type_id else None

        # Fallback location
        stock_location = self.env.ref('stock.stock_location_stock', raise_if_not_found=False)

        # # Step 1: Get latest confirmed PO
        # purchase_dua = self.env['purchase.order'].search([
            # ('state', '=', 'purchase')
        # ], order='date_order desc', limit=1)
        
        # Step 1: Use PO from context if available
        purchase_id = self.env.context.get('default_purchase_dua_id')
        purchase_dua = self.env['purchase.order'].browse(purchase_id) if purchase_id else self.env['purchase.order'].search([
            ('state', '=', 'purchase')
        ], order='date_order desc', limit=1)

        if not purchase_dua:
            return res

        res['purchase_dua_id'] = purchase_dua.id
        po = purchase_dua

        res.update({
            'partner_vendor_id': po.partner_id.id,
            'user_id': po.user_id.id,
            'resquester_user_id': po.requester_user_id.id,
            'created_user_id': po.created_user_id.id,
            'validity_date': po.validity_period,
            'cip_id': po.phase_project_id.id,
        })

        move_lines = []
        transaction_date = fields.Date.context_today(self)
        usd_currency = self.env['res.currency'].search([('name', '=', 'USD')], limit=1)
        idr_currency = self.env['res.currency'].search([('name', '=', 'IDR')], limit=1)

        for line in po.order_line:
            qty_in_draft = sum(self.env['stock.move'].search([
                ('picking_id.state_fppr', 'in', ['draft', 'pending_approval']),
                ('purchase_order_line_id', '=', line.id)
            ]).mapped('quantity'))

            remaining_qty = line.product_qty - (line.qty_received + qty_in_draft)
            if remaining_qty <= 0:
                continue

            new_currency_id = line.new_currency_id
            accounting_date = transaction_date

            period_line = self.env['invoicing.period.line'].search([
                ('date_start', '<=', transaction_date),
                ('date_end', '>=', transaction_date),
                ('invoice_period_id.move_type', '=', 'in_invoice')
            ], limit=1)

            if transaction_date.day > (period_line.close_date.day if period_line and period_line.close_date else 25):
                accounting_date = (transaction_date + relativedelta(months=1)).replace(day=1)

            if new_currency_id.is_budget or new_currency_id.name == 'USD':
                price_unit = usd_currency._convert(line.price_unit, idr_currency, self.env.company, date=accounting_date)
                subtotal_currency = (line.subtotal_currency / line.product_qty) * remaining_qty if line.product_qty else 0
            else:
                price_unit = line.price_unit
                subtotal_currency = (line.subtotal_currency / line.product_qty) * remaining_qty if line.product_qty else 0

            move_lines.append((0, 0, {
                'product_id': line.product_id.id,
                'product_tmpl_id': line.product_tmpl_id.id,
                'product_uom_qty': remaining_qty,
                'quantity': remaining_qty,
                'rkap_code': po.rkap_id.rkap_code,
                'unit_price': price_unit,
                'product_uom_id': line.product_uom.id,
                'name': line.name or line.product_id.display_name,
                'currency_id': new_currency_id.id,
                'ammount_currency': subtotal_currency,
                'purchase_order_line_id': line.id,
                'purchase_line_id': line.id,
                'location_id': picking_type.default_location_src_id.id if picking_type and picking_type.default_location_src_id else (stock_location.id if stock_location else False),
                'location_dest_id': picking_type.default_location_dest_id.id if picking_type and picking_type.default_location_dest_id else (stock_location.id if stock_location else False),
            }))

        res['move_ids_without_package'] = move_lines
        return res

    @api.depends('move_ids_without_package.ammount_currency', 'move_ids_without_package.currency_id')
    def _compute_amount_currency(self):
        for picking in self:
            total = 0.0
            company = picking.company_id
            company_currency = company.currency_id if company else None
            today = fields.Date.today()

            for move in picking.move_ids_without_package:
                if move.currency_id and move.ammount_currency and company and company_currency:
                    if move.currency_id != company_currency:
                        # Use main_currency_id if available, otherwise fallback to move.currency_id
                        main_currency = move.currency_id.main_currency_id or move.currency_id

                        converted_amount = main_currency._convert(
                            move.ammount_currency,
                            company_currency,
                            company,
                            move.date or today
                        )
                    else:
                        converted_amount = move.ammount_currency

                    total += converted_amount

            picking.invoice_amount = total

    def button_validate(self):
        res = super().button_validate()

        for picking in self:
            if picking.picking_type_id.code != 'incoming':
                continue

            po_list = picking.purchase_id or picking.move_ids.purchase_line_id.mapped('order_id')
            vendor_bills = self.env['account.move'].search([
                ('purchase_id', 'in', po_list.ids),
                ('move_type', '=', 'in_invoice'),
                ('state', 'in', ['draft', 'posted'])
            ])

            for bill in vendor_bills:
                if picking.date_done:
                    bill.date = picking.date_done
                    bill.invoice_date = picking.date_done

        return res
    
    @api.depends('transaction_date', 'validity_date')
    def _compute_mandatory_attachment(self):
        for record in self:
            record.is_mandatory_attachment_nodin = (
                record.transaction_date and record.validity_date
                and record.transaction_date > record.validity_date
            )  

    @api.onchange('transaction_date')
    def _compute_transaction_date(self):
        if self.transaction_date:
            if isinstance(self.transaction_date, str):
                transaction_dt = fields.Date.from_string(self.transaction_date)
            else:
                transaction_dt = self.transaction_date

            if transaction_dt.day > 25:
                next_month = transaction_dt.replace(day=1) + timedelta(days=32)
                self.accounting_date = next_month.replace(day=1)
            else:
                self.accounting_date = transaction_dt
                
    
    # _sql_constraints = [
        # ("nomor_invoice_unique", "unique(nomor_invoice)", "Nomor Invoice must be unique"),
    # ]
    
    @api.depends('nilai_akhir')
    def _compute_description(self):
        for rec in self:
            description = rec.env['assesment.score'].search([('start_scale', '<=', int(rec.nilai_akhir)), ('end_scale', '>=', int(rec.nilai_akhir))])
            if description:
                rec.description = description.definition
            elif not description and rec.nilai_akhir > 0:
                raise ValidationError(_("Value 'Description' is not set. Contact your administrator to complete the 'Master Final Score Definition."))
            else:
                rec.description = ''

    @api.depends('assesment_purchase_line_ids.bobot_nilai')
    def _compute_total_bobot(self):
        for rec in self:
            rec.total_bobot = sum(rec.assesment_purchase_line_ids.mapped('bobot_nilai'))
    
    @api.depends('total_bobot')
    def _compute_nilai_akhir(self):
        for rec in self:
            rec.nilai_akhir = (rec.total_bobot * 100) / 4

    @api.onchange('purchase_dua_id')
    def onchange_purchase_id(self):
        if not self.purchase_dua_id:
            return

        po = self.purchase_dua_id
        self.partner_vendor_id = po.partner_id.id
        self.user_id = po.user_id.id
        self.resquester_user_id = po.requester_user_id.id
        self.created_user_id = po.created_user_id.id
        self.validity_date = po.validity_period
        self.cip_id = po.phase_project_id

        self.move_ids_without_package = [(5, 0, 0)]
        move_lines = []
        usd_currency = self.env['res.currency'].search([('name', '=', 'USD')], limit=1)
        idr_currency = self.env['res.currency'].search([('name', '=', 'IDR')], limit=1)
        company = self.company_id or self.env.company
        stock_location = self.env.ref('stock.stock_location_stock', raise_if_not_found=False)

        for line in po.order_line:
            qty_in_draft = sum(self.env['stock.move'].search([
                ('picking_id.state_fppr', 'in', ['draft', 'pending_approval']),
                ('purchase_order_line_id', '=', line.id)
            ]).mapped('quantity'))

            remaining_qty = line.product_qty - (line.qty_received + qty_in_draft)
            if remaining_qty <= 0:
                continue

            new_currency_id = line.new_currency_id
            accounting_date = self.transaction_date or fields.Date.context_today(self)

            period_line = self.env['invoicing.period.line'].search([
                ('date_start', '<=', accounting_date),
                ('date_end', '>=', accounting_date),
                ('invoice_period_id.move_type', '=', 'in_invoice')
            ], limit=1)

            if self.transaction_date:
                if period_line and period_line.close_date and self.transaction_date.day > period_line.close_date.day:
                    accounting_date = (self.transaction_date + relativedelta(months=1)).replace(day=1)
                elif self.transaction_date.day > 25:
                    accounting_date = (self.transaction_date + relativedelta(months=1)).replace(day=1)

            if new_currency_id.is_budget or new_currency_id.name == 'USD':
                price_unit = usd_currency._convert(line.price_unit, idr_currency, company, date=accounting_date)
                subtotal_currency = (line.subtotal_currency / line.product_qty) * remaining_qty if line.product_qty else 0
            else:
                price_unit = line.price_unit
                subtotal_currency = (line.subtotal_currency / line.product_qty) * remaining_qty if line.product_qty else 0

            move_lines.append((0, 0, {
                'product_id': line.product_id.id,
                'product_tmpl_id': line.product_tmpl_id.id,
                'product_uom_qty': remaining_qty,
                'quantity': remaining_qty,
                'rkap_code': po.rkap_id.rkap_code,
                'unit_price': price_unit,
                'product_uom_id': line.product_uom.id,
                'name': line.name or line.product_id.display_name,
                'currency_id': new_currency_id.id,
                'ammount_currency': subtotal_currency,
                'purchase_order_line_id': line.id,
                'purchase_line_id': line.id,
                'location_id': self.location_id.id or (stock_location.id if stock_location else False),
            }))

        self.move_ids_without_package = move_lines

    @api.constrains('move_ids_without_package', 'move_ids_without_package.quantity', 'move_ids_without_package.ammount_currency')
    def _constrains_move_ids_without_package(self):
        for record in self:
            move_lines = record.move_ids_without_package
            for move in move_lines:
                if move.purchase_order_line_id and move.ammount_currency > move.purchase_order_line_id.price_subtotal:
                    raise ValidationError(_(
                        "Amount currency %s tidak boleh lebih besar dari subtotal %s pada baris terkait."
                    ) % (move.ammount_currency, move.purchase_order_line_id.price_subtotal))
                elif move.purchase_order_line_id and move.quantity > move.purchase_order_line_id.product_qty:
                    raise ValidationError(_(
                        "Quantity %s tidak boleh lebih besar dari quantity %s pada baris terkait."
                    ) % (move.quantity, move.purchase_order_line_id.product_qty))

    @api.model
    def create(self, vals):
        picking_type = self.env['stock.picking.type'].browse(vals.get('picking_type_id'))

        # Check only for incoming GR
        if vals.get('purchase_dua_id') and picking_type.code == 'incoming':
            purchase = self.env['purchase.order'].browse(vals['purchase_dua_id'])

            # Block if all products are already fully received (including draft/pending moves)
            fully_received = True
            for line in purchase.order_line:
                # Calculate already received quantity including non-done GRs
                qty_received_not_done = sum(self.env['stock.move'].search([
                    ('picking_id.state', 'not in', ['done', 'cancel']),
                    ('purchase_order_line_id', '=', line.id)
                ]).mapped('quantity'))

                total_received = line.qty_received + qty_received_not_done
                if total_received < line.product_qty:
                    fully_received = False
                    break

            if fully_received:
                raise ValidationError(_("Semua produk di Purchase Order ini sudah diterima seluruhnya."))

            # Optional: block if there's already a pending GR (prevent multiple drafts)
            existing = self.env['stock.picking'].search([
                ('purchase_dua_id', '=', vals['purchase_dua_id']),
                ('picking_type_id', '=', vals['picking_type_id']),
                ('state', 'not in', ['done', 'cancel']),
            ])
            if existing:
                raise ValidationError(_("Sudah ada dokumen Receipt untuk Purchase Order ini yang belum selesai."))

        # Create GR
        res = super(StockPicking, self).create(vals)

        # Block if accounting period already closed
        if res.accounting_period_id and res.accounting_period_id.state == 'done':
            raise ValidationError(_('Accounting Period sudah closed'))

        # Auto-create assessment lines if applicable
        aspect_lines = self.env['assesment.aspect.line'].search([('aspect_id.name', '=', 'fppr')])
        if aspect_lines and not vals.get('assesment_purchase_line_ids'):
            res.assesment_purchase_line_ids = [(0, 0, {
                'assesment_aspect': aspect.name,
                'persentasi_bobot': aspect.weight
            }) for aspect in aspect_lines]

        return res

    def write(self, vals):
        res = super(StockPicking, self).write(vals)
        if self.accounting_period_id.state == 'done':
            raise ValidationError('Accounting Period sudah closed')
        return res


    def action_open_journal(self):
        return {
            'name': 'Journal',
            'type': 'ir.actions.act_window',
            'res_model': 'account.move',
            'view_mode': 'list,form',
            'domain': [('is_journal_gr', '=', True), ('picking_journal_gr_id', '=', self.id)],
            'context': {
                'create': False,
                'edit': False, 
                },
        }

    def action_open_journal_bill(self):
        return {
            'name': 'Vendor Bill',
            'type': 'ir.actions.act_window',
            'res_model': 'account.move',
            'view_mode': 'list,form',
            'views': [(self.env.ref('vendor_bill_linkaja.view_in_invoice_bill_tree_inherit_vendor_bill_linkaja').id, 'list'), (self.env.ref('vendor_bill_linkaja.view_move_form_ext').id, 'form')],
            'domain': [('is_journal_bill', '=', True), ('picking_journal_bill_id', '=', self.id)],
            'context': {
                'create': False,
                # 'edit': False, 
                },
        }

    def action_button_submit(self):
        self.action_confirm()
        self.write({'state': 'assigned'})
        self.write({'state_fppr': 'pending_approval'})
        # self.write({'state_fppr': 'pending_approval', 'state': 'done'})
        for rec in self.assesment_purchase_line_ids:
            if not rec.score_penilaian:
                raise ValidationError(_("Please fill in the score for %s") % rec.assesment_aspect)


    def action_button_return_to_draft(self):
        for picking in self:
            # === Cancel Journal Entry (in_receipt) ===
            journal_moves = self.env['account.move'].search([
                ('picking_journal_gr_id', '=', picking.id),
                ('move_type', '=', 'in_receipt')
            ])
            for move in journal_moves:
                if move.state == 'posted':
                    move.button_draft()
                    move.button_cancel()
                move.unlink()

            # === Cancel Vendor Bill (in_invoice) ===
            vendor_bills = self.env['account.move'].search([
                ('picking_journal_bill_id', '=', picking.id),
                ('move_type', '=', 'in_invoice')
            ])
            for bill in vendor_bills:
                bill.is_validate_before = False
                bill.is_initiate_before = False
                if bill.validation_state == 'validate':
                    bill.is_validate_before = True
                elif bill.validation_state == 'initiate_approval':
                    bill.is_initiate_before = True
                if bill.state == 'posted':
                    bill.button_draft()
                    # bill.button_cancel()
                else:
                    bill.write({'validation_state': 'draft'})
                # Unlink reconciliations before deleting
                bill.line_ids.remove_move_reconcile()
                # bill.unlink()

            # === Reset GR States ===
            picking.write({
                'state_fppr': 'draft',
                'state': 'draft',
                'invoice_move_id': False,
            })

            # === Reset PO new_state ===
            if picking.purchase_dua_id:
                # Optional: reset qty_received if needed (Odoo computes this automatically if no stock move is done)
                # for line in picking.purchase_dua_id.order_line:
                #     line.qty_received = 0

                picking.purchase_dua_id.write({
                    'new_state': 'open',
                    'bill_state': 'no',  # optionally reset to 'no' or 'to bill'
                })

    def action_button_return_of_documents(self):
        for picking in self:
            # Cancel Journal Entry (in_receipt)
            journal_moves = self.env['account.move'].search([
                ('picking_journal_gr_id', '=', picking.id),
                ('move_type', '=', 'in_receipt')
            ])
            for move in journal_moves:
                if move.state == 'posted':
                    move.button_draft()
                    move.button_cancel()
                move.unlink()

            # Cancel Vendor Bill (in_invoice)
            vendor_bills = self.env['account.move'].search([
                ('picking_journal_bill_id', '=', picking.id),
                ('move_type', '=', 'in_invoice')
            ])
            for bill in vendor_bills:
                if bill.state == 'posted':
                    bill.button_draft()
                    bill.button_cancel()
                # Remove reconciliations (if any) and unlink
                bill.line_ids.remove_move_reconcile()
                bill.unlink()

            # Reset state
            picking.write({
                'state_fppr': 'draft',
                'state': 'draft'
            })

    def action_button_return_of_goods(self):
        for picking in self:
            # Cancel Journal Entry (in_receipt)
            journal_moves = self.env['account.move'].search([
                ('picking_journal_gr_id', '=', picking.id),
                ('move_type', '=', 'in_receipt')
            ])
            for move in journal_moves:
                if move.state == 'posted':
                    move.button_draft()
                    move.button_cancel()
                move.unlink()

            # Cancel Vendor Bill (in_invoice)
            vendor_bills = self.env['account.move'].search([
                ('picking_journal_bill_id', '=', picking.id),
                ('move_type', '=', 'in_invoice')
            ])
            for bill in vendor_bills:
                if bill.state == 'posted':
                    bill.button_draft()
                    bill.button_cancel()
                # Remove reconciliations (if any) and unlink
                bill.line_ids.remove_move_reconcile()
                bill.unlink()

        self.write({'state_fppr': 'ap_invoice'})
        self.write({'state': 'done'})

    def action_button_cancel(self):
        self.write({'state_fppr': 'cancel'})
        self.write({'state': 'cancel'})

    def action_button_withdraw_approval(self):
        self.write({'state_fppr': 'pending_approval'})
        self.write({'state': 'assigned'})

    def action_approve(self):
        usd_currency = self.env['res.currency'].search([('name', '=', 'USD')], limit=1)
        idr_currency = self.env['res.currency'].search([('name', '=', 'IDR')], limit=1)
        company = self.company_id or self.env.company
        move_lines = self.move_ids_without_package
        po = self.purchase_dua_id

        # Group lines by category
        category_map = {}
        credit_account_id = False
        bill_credit_account_id = self.partner_vendor_id.property_account_payable_id.id
        bill_debit_account_id = False

        journal = False
        for line in move_lines:
            category = line.product_tmpl_id.categ_id
            journal = category.property_stock_journal.id if category.property_stock_journal else False
            if category not in category_map:
                category_map[category] = []
            category_map[category].append(line)
            if category.property_stock_account_input_categ_id:
                credit_account_id = category.property_stock_account_input_categ_id.id
                bill_debit_account_id = credit_account_id

        # for category, lines in category_map.items():
        #     journal = category.property_stock_journal.id if category.property_stock_journal else False
        #     if not journal:
        #         raise UserError(f"Kategori produk '{category.name}' tidak memiliki 'property_stock_journal'.")

        line_ids = []
        for line in move_lines:
            rate = 1
            if line.purchase_order_line_id.new_currency_id.is_budget:
                accounting_date = self.transaction_date or fields.Date.context_today(self)
                period_line = self.env['invoicing.period.line'].search([
                    ('date_start', '<=', accounting_date),
                    ('date_end', '>=', accounting_date),
                    ('invoice_period_id.move_type', '=', 'in_invoice')
                ], limit=1)
                if period_line and period_line.close_date:
                    if accounting_date.day > period_line.close_date.day:
                        accounting_date = (accounting_date + relativedelta(months=1)).replace(day=1)
                elif accounting_date.day > 25:
                    accounting_date = (accounting_date + relativedelta(months=1)).replace(day=1)

                rate = usd_currency._convert(
                    line.purchase_order_line_id.price_unit * line.purchase_order_line_id.product_qty,
                    idr_currency,
                    company,
                    date=accounting_date
                ) / (line.purchase_order_line_id.price_unit * line.purchase_order_line_id.product_qty or 1)

            line_ids.append((0, 0, {
                'product_template_id': line.product_tmpl_id.id,
                'description': line.product_tmpl_id.name,
                'product_id': line.product_id.id,
                'quantity': line.quantity,
                'price_unit': line.purchase_order_line_id.price_unit * rate,
                'new_unit_price': line.purchase_order_line_id.price_unit,
                'name': line.name or line.product_id.display_name,
                'currency_conversion_id': usd_currency.id if line.purchase_order_line_id.new_currency_id.is_budget else line.purchase_order_line_id.new_currency_id.id,
                'inverse_conv_rate': rate,
                'manual_conversion': line.purchase_order_line_id.currency_rate > 1,
            }))

        journal_entry = self.env['account.move'].with_context(skip_check_journal_type=True).create({
            'move_type': 'in_receipt',
            'auto_post': 'no',
            'partner_id': self.partner_vendor_id.id,
            'journal_id': journal,
            'invoice_date': self.accounting_date,
            'date': self.accounting_date,
            'currency_id': self.currency_id.id,
            'is_journal_gr': True,
            'picking_journal_gr_id': self.id,
            'name': self.name,
            'ref': self.name,
            'invoice_line_ids': line_ids,
        })
        for move_line in journal_entry.line_ids:
            move_line.rkap_code = po.rkap_code
            move_line.group_id = po.group_id.id

        for line in journal_entry.line_ids.filtered(lambda x: x.credit > 0):
            line.account_id = credit_account_id

        # Vendor Bill
        vendor_contact_id = self.env['res.partner'].search([
            ('company_type', '=', 'contact'),
            ('parent_id', '=', self.partner_vendor_id.id)
        ], limit=1)

        invoice_line_ids = []
        for line in move_lines:
            rate = 1
            if line.purchase_order_line_id.new_currency_id.is_budget:
                accounting_date = self.transaction_date or fields.Date.context_today(self)
                period_line = self.env['invoicing.period.line'].search([
                    ('date_start', '<=', accounting_date),
                    ('date_end', '>=', accounting_date),
                    ('invoice_period_id.move_type', '=', 'in_invoice')
                ], limit=1)
                if period_line and period_line.close_date:
                    if accounting_date.day > period_line.close_date.day:
                        accounting_date = (accounting_date + relativedelta(months=1)).replace(day=1)
                elif accounting_date.day > 25:
                    accounting_date = (accounting_date + relativedelta(months=1)).replace(day=1)

                rate = usd_currency._convert(
                    line.purchase_order_line_id.price_unit * line.purchase_order_line_id.product_qty,
                    idr_currency,
                    company,
                    date=accounting_date
                ) / (line.purchase_order_line_id.price_unit * line.purchase_order_line_id.product_qty or 1)

            invoice_line_ids.append((0, 0, {
                'product_template_id': line.product_tmpl_id.id,
                'description': line.product_tmpl_id.name,
                'product_id': line.product_id.id,
                'quantity': line.ammount_currency / line.purchase_order_line_id.price_unit,
                'price_unit': line.purchase_order_line_id.price_unit * rate,
                'new_unit_price': line.purchase_order_line_id.price_unit,
                'name': line.name or line.product_id.display_name,
                'currency_conversion_id': usd_currency.id if line.purchase_order_line_id.new_currency_id.is_budget else line.purchase_order_line_id.new_currency_id.id,
                'inverse_conv_rate': rate,
                'manual_conversion': line.purchase_order_line_id.currency_rate > 1,
                'stock_move_id': line.id,
            }))

        bill = self.env['account.move'].search([('picking_journal_bill_id', '=', self.id)], limit=1)
        if bill:
            bill.invoice_line_ids.with_context(create_non_manual=True, dynamic_unlink=True).unlink()
            bill.with_context(dynamic_unlink=True, create_non_manual=True).invoice_line_ids = invoice_line_ids
            bill.write({
                # 'auto_post': 'no',
                'partner_id': self.partner_vendor_id.id,
                'vendor_contact_id': vendor_contact_id.id if vendor_contact_id else False,
                'journal_id': self.env['account.journal'].search([('type', '=', 'purchase')], limit=1).id,
                'invoice_date': self.accounting_date,
                'date': self.accounting_date,
                'currency_id': self.currency_id.id,
                'is_journal_bill': True,
                'ref': self.name,
                'contact_email': po.email_contact,
                'unit_id': po.unit_id.id,
                'group_department_id': po.group_id.id,
                'budgetary_position_id': po.purchase_request_id.justification_id.budgetary_position_id.id,
                'rkap_code': po.rkap_code,
                'rkap_category_id': po.rkap_category_id.id,
                'rkap_type_id': po.rkap_type_id.id,
                # 'name': '/',
                # 'source': 'purchase',
                'purchase_order_id': po.id,
                # 'invoice_line_ids': invoice_line_ids,
                'project_id': self.cip_id.id,
                'payment_reference': self.nomor_invoice,
                'requestor_id': self.resquester_user_id.employee_ids[0].id if self.resquester_user_id and self.resquester_user_id.employee_ids else False,
            })
            if bill.is_validate_before:
                bill.action_submit()
            if bill.is_initiate_before:
                bill.action_submit()
        else:
            bill = self.env['account.move'].with_context(create_non_manual=True).create({
                'move_type': 'in_invoice',
                'auto_post': 'no',
                'partner_id': self.partner_vendor_id.id,
                'vendor_contact_id': vendor_contact_id.id if vendor_contact_id else False,
                'journal_id': self.env['account.journal'].search([('type', '=', 'purchase')], limit=1).id,
                'invoice_date': self.accounting_date,
                'date': self.accounting_date,
                'currency_id': self.currency_id.id,
                'is_journal_bill': True,
                'picking_id': self.id,
                'ref': self.name,
                'contact_email': po.email_contact,
                'unit_id': po.unit_id.id,
                'group_department_id': po.group_id.id,
                'budgetary_position_id': po.purchase_request_id.justification_id.budgetary_position_id.id,
                'rkap_code': po.rkap_code,
                'rkap_category_id': po.rkap_category_id.id,
                'rkap_type_id': po.rkap_type_id.id,
                'name': '/',
                'picking_journal_bill_id': self.id,
                'source': 'purchase',
                'purchase_order_id': po.id,
                'invoice_line_ids': invoice_line_ids,
                'project_id': self.cip_id.id,
                'payment_reference': self.nomor_invoice,
                'requestor_id': self.resquester_user_id.employee_ids[0].id if self.resquester_user_id and self.resquester_user_id.employee_ids else False,
            })

        for line in bill.line_ids.filtered(lambda x: x.debit > 0):
            line.with_context({'no_check': True}).write({
                'account_id': line.product_template_id.categ_id.property_stock_account_input_categ_id.id,
            })

        for line in bill.line_ids.filtered(lambda x: x.credit > 0):
            line.with_context({'no_check': True}).write({'account_id': bill_credit_account_id})

        # Assign bill
        self.invoice_move_id = bill.id

        # Mark custom state
        self.write({
            'state_fppr': 'ap_invoice',
            'state': 'done',
        })

    def action_reject(self):
        self.write({'state_fppr': 'draft'})
    
    @api.onchange('location_id')
    def _onchange_location_id_set_to_moves(self):
        if self.picking_type_id.code == 'incoming':
            for move in self.move_ids_without_package:
                move.location_id = self.location_id.id

class StockMove(models.Model):
    _inherit = 'stock.move'

    rkap_code_id = fields.Many2one('account.budget.post', string='RKAP Code')
    rkap_code = fields.Char('RKAP Code')
    product_uom_id = fields.Many2one('uom.uom', string='Units')
    ammount_currency = fields.Monetary('Currency', readonly=False, currency_field='company_currency_id')
    currency_id = fields.Many2one('res.currency', string='currency', ondelete='set null', default=lambda self: self.env.company.currency_id)
    company_currency_id = fields.Many2one('res.currency', string='Company Currency', related='company_id.currency_id', store=True, readonly=True)
    unit_price = fields.Float('Unit Price')
    quantity = fields.Float('Quantity', compute='_compute_quantity', store=True)
    stock_location_internal_id = fields.Many2one('stock.location', string='Stock Location', domain=[('usage', '=', 'internal')])
    asset_location_id = fields.Many2one('asset.location', string='Asset Location')
    product_tmpl_id = fields.Many2one('product.template', string='Product Variant', related="", store=True)
    location_id = fields.Many2one('stock.location', required=True)
    purchase_order_line_id = fields.Many2one('purchase.order.line', 'Purchase Order Line')

    @api.depends('ammount_currency')
    def _compute_quantity(self):
        for rec in self:
            rec.quantity = 0
            if rec.ammount_currency and rec.purchase_order_line_id and rec.purchase_order_line_id.price_unit:
                try:
                    rec.quantity = rec.ammount_currency / rec.purchase_order_line_id.price_unit
                except ZeroDivisionError:
                    raise ValidationError(_("Unit Price cannot be zero."))
            if rec.ammount_currency == 0 and not self.env.context.get('from_so') and not self.env.context.get('create_siv'):
                raise ValidationError(_("Amount cannot be zero."))
            if rec.purchase_order_line_id and rec.ammount_currency > rec.purchase_order_line_id.price_subtotal:
                raise ValidationError(_(
                    "Amount currency %s tidak boleh lebih besar dari subtotal %s pada baris terkait."
                ) % (rec.ammount_currency, rec.purchase_order_line_id.price_subtotal))

    @api.onchange('ammount_currency')
    def _onchange_ammount_currency(self):
        for rec in self:
            if rec.purchase_order_line_id and rec.ammount_currency > rec.purchase_order_line_id.price_subtotal:
                raise ValidationError(_(
                    "Amount currency %s tidak boleh lebih besar dari subtotal %s pada baris terkait."
                ) % (rec.ammount_currency, rec.purchase_order_line_id.price_subtotal))

    @api.onchange('quantity')
    def _onchange_quantity(self):
        for rec in self:
            if rec.purchase_order_line_id:
                rec.ammount_currency = (rec.purchase_order_line_id.subtotal_currency / rec.purchase_order_line_id.product_qty) * rec.quantity if rec.purchase_order_line_id.product_qty != 0 else 0
            if rec.purchase_order_line_id and rec.quantity > rec.purchase_order_line_id.product_qty:
                raise ValidationError(_(
                    "Quantity %s tidak boleh lebih besar dari quantity %s pada baris terkait."
                ) % (rec.quantity, rec.purchase_order_line_id.product_qty))
    
    @api.onchange('stock_location_internal_id')
    def _onchange_location_id(self):
        for rec in self:
            if rec.stock_location_internal_id:
                rec.location_id = rec.stock_location_internal_id.id
            else:
                rec.location_id = False
            


class ApprovalGrLine(models.Model):
    _name = 'approval.gr.line'

    sequence = fields.Char('sequence')
    approval_name = fields.Char('Approval Name')
    approval_position = fields.Char('Approval Position')
    approval_status = fields.Char('Approval Status')
    approval_date = fields.Date('Approval Date')
    approval_note = fields.Char('Approval Note')
    reassign_to = fields.Char('Reassign To')
    approval_status_dua = fields.Char('Approval Status')
    approval_date_dua = fields.Date('Approval Date')
    approval_note_dua = fields.Char('Approval Note')
    picking_id = fields.Many2one('stock.picking', string='picking', ondelete='cascade')

    