from odoo import models, fields, api, _
from odoo.exceptions import UserError
import logging
import io
import xlsxwriter
import base64

_logger = logging.getLogger(__name__)


class PrPoSlaWizard(models.TransientModel):
    _name = 'pr.po.sla.wizard'
    _description = 'PR PO SLA Wizard'

    start_date = fields.Date(string='Start PO Created Date', required=True)
    end_date = fields.Date(string='End PO Created Date', required=True)

    def _get_sla_data(self):
        # Ambil model dan nama tabel relasi yang tepat
        purchase_request = self.env['purchase.request']
        justification_model = purchase_request._fields['justification_id'].comodel_name
        agreement_model = purchase_request._fields['agreement_id'].comodel_name
        negotiation_model = self.env[agreement_model]._fields['negotiation_id'].comodel_name

        justification_table = self.env[justification_model]._table
        agreement_table = self.env[agreement_model]._table
        negotiation_table = self.env[negotiation_model]._table

        query = f"""
            SELECT
                pr.name AS pr_number,
                TO_CHAR(jus.date, 'DD/MM/YYYY') AS pr_assign_date,
                STRING_AGG(po.name, ', ') AS po_number,
                TO_CHAR(pr.create_date::date, 'DD/MM/YYYY') AS po_created_date,
                ag.name AS mpa_number,
                nego.name AS baf_number,
                CASE WHEN pr.dpl_id IS NOT NULL THEN 'Yes' ELSE 'No' END AS dpl_name
            FROM purchase_request pr
            LEFT JOIN purchase_order po ON po.purchase_request_id = pr.id
            LEFT JOIN {justification_table} jus ON jus.id = pr.justification_id
            LEFT JOIN {agreement_table} ag ON ag.id = pr.agreement_id
            LEFT JOIN {negotiation_table} nego ON nego.id = ag.negotiation_id
            WHERE pr.create_date::date BETWEEN %s AND %s
            GROUP BY pr.name, jus.date, pr.create_date, ag.name, nego.name, pr.dpl_id
            ORDER BY pr.create_date
        """

        try:
            self.env.cr.execute(query, (self.start_date, self.end_date))
            rows = self.env.cr.dictfetchall()
        except Exception as e:
            raise UserError(_('Gagal mengambil data SLA PR-PO. Detail: %s') % str(e))

        if not rows:
            raise UserError(_('Tidak ada data PR pada rentang tanggal tersebut.'))

        _logger.debug("Data SLA PR-PO (SQL): %s", rows)
        return rows

    def export_excel(self):
        data = self._get_sla_data()

        output = io.BytesIO()
        workbook = xlsxwriter.Workbook(output, {'in_memory': True})
        sheet = workbook.add_worksheet('PR PO SLA')

        bold = workbook.add_format({'bold': True, 'border': 1, 'bg_color': '#D9E1F2', 'align': 'center'})
        border = workbook.add_format({'border': 1})

        headers = ['PR Number', 'PR Assign Date', 'PO Number', 'PO Created Date',
                   'MPA Number', 'BAF Number', 'DPL']
        sheet.write_row(0, 0, headers, bold)

        for row_idx, row in enumerate(data, start=1):
            sheet.write(row_idx, 0, row.get('pr_number') or '', border)
            sheet.write(row_idx, 1, row.get('pr_assign_date') or '', border)
            sheet.write(row_idx, 2, row.get('po_number') or '', border)
            sheet.write(row_idx, 3, row.get('po_created_date') or '', border)
            sheet.write(row_idx, 4, row.get('mpa_number') or '', border)
            sheet.write(row_idx, 5, row.get('baf_number') or '', border)
            sheet.write(row_idx, 6, row.get('dpl_name') or '', border)

        sheet.set_column('A:G', 20)
        workbook.close()
        output.seek(0)

        file_data = base64.b64encode(output.read())

        attachment = self.env['ir.attachment'].create({
            'name': 'pr_po_sla_report.xlsx',
            'type': 'binary',
            'datas': file_data,
            'res_model': self._name,
            'res_id': self.id,
            'mimetype': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        })

        return {
            'type': 'ir.actions.act_url',
            'url': '/web/content/%s?download=true' % attachment.id,
            'target': 'self',
        }

    def export_pdf(self):
        data = self._get_sla_data()
        return self.env.ref('ap_report_sla.action_report_pr_po_sla').report_action(self, data={
            'records': data,
            'start_date': self.start_date.strftime('%d/%m/%Y') if self.start_date else '',
            'end_date': self.end_date.strftime('%d/%m/%Y') if self.end_date else '',
        })
