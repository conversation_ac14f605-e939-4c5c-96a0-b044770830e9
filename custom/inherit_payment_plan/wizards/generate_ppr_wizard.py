from odoo import fields, models, api, _
from datetime import datetime, timedelta, date
from odoo.exceptions import ValidationError



class GeneratePPRWizard(models.TransientModel):
    _name = 'generate.ppr.wizard'
    _description = 'Generate PPR Wizard'

    date = fields.Date('Date', default=date.today())
    requester_user_by_id = fields.Many2one('res.users', string='Requester By', default=lambda self: self.env.user)
    pay_through_days = fields.Integer('Pay Through Days', required=False)
    pay_through_date = fields.Date('Pay Through Date', required=False)

    @api.onchange('date', 'pay_through_days')
    def _onchange_pay_through_days(self):
        if self.date:
            self.pay_through_date = self.date + timedelta(days=self.pay_through_days)

    def action_generate_ppr(self):
        active_ids = self._context.get('active_ids')
        bills = self.env['account.move'].search([('id', 'in', active_ids)])
        pay_groups_id = False
        partner_ids = []
        for bill in bills:
            for bank in bill.partner_id.bank_ids:
                search_pay_groups = self.env['account.pay.groups'].search([('bank_account_id', '=', bank.id)], limit=1)
                if search_pay_groups:
                    pay_groups_id = search_pay_groups.id
            if bill.partner_id.id not in partner_ids:
                partner_ids.append(bill.partner_id.id)
        create_ppr = self.env['account.payment.plan'].create({
            'name': 'New',
            'date': self.date,
            'pay_through_days': self.pay_through_days,
            'pay_through_date': self.pay_through_date,
            'requester_user_by_id': self.requester_user_by_id.id,
            'selection': 'vendor',
            'partner_ids': partner_ids,
            'pay_groups_id': pay_groups_id,
        })
        for bill in bills:
            amount_due = bill.amount_total
            pp_line_exist = self.env['account.payment.plan.line'].search([('move_id', '=', bill.id), ('state', 'not in', ['rejected', 'cancel'])])
            for pp_line in pp_line_exist:
                amount_due -= pp_line.amount_to_pay
            line = self.env['account.payment.plan.line'].create({
                'payment_plan_id': create_ppr.id,
                'move_id': bill.id,
                'is_process': True,
                'amount_to_pay': amount_due,
            })
            line.onchange_bill()
        return {
            'name': _('Payment Process Request'),
            'type': 'ir.actions.act_window',
            'res_model': 'account.payment.plan',
            'view_mode': 'form',
            'res_id': create_ppr.id,
            'target': 'current',
            'context': {'edit': True},
        }
