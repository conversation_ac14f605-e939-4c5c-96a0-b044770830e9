<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <data>
    
        <record id="account_pay_groups_view_form" model="ir.ui.view">
            <field name="name">account.pay.groups.view.form</field>
            <field name="model">account.pay.groups</field>
            <field name="arch" type="xml">
                <form string="Account Pay Groups">
                    <sheet>
                        <group>
                            <field name="pay_groups" />
                            <field name="account_ids" widget="many2many_tags" string="Bank"/>
                            <field name="bank_account_id"/>
                            <field name="flag"/>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <record id="account_pay_groups_view_tree" model="ir.ui.view">
            <field name="name">account.pay.groups.view.tree</field>
            <field name="model">account.pay.groups</field>
            <field name="arch" type="xml">
                <list string="Account Pay Groups">
                    <field name="pay_groups" />
                    <field name="account_ids" string="Bank"/>
                    <field name="flag" />
                </list>
            </field>
        </record>

        <record id="account_pay_groups_action" model="ir.actions.act_window">
            <field name="name">Account Pay Groups</field>
            <field name="res_model">account.pay.groups</field>
            <field name="view_mode">list,form</field>
            <field name="help" type="html">
                <h1>Kosong</h1>
            </field>
        </record>

        <menuitem
            id="account_pay_groups_menu"
            name="Account Pay Groups"
            parent="account.menu_finance_configuration"
            sequence="100"/>

        <menuitem
            id="pay_groups_menu"
            name="Pay Groups"
            action="account_pay_groups_action"
            parent="account_pay_groups_menu"
            sequence="10"/>
    
    </data>
    

</odoo>
