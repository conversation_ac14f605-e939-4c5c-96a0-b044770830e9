<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="action_bill_outstanding" model="ir.actions.act_window">
            <field name="name">Outstanding Bills</field>
            <field name="res_model">account.move</field>
<!--            <field name="path">bills</field>-->
            <field name="view_mode">list,form</field>
            <field name="view_id" ref="account.view_in_invoice_bill_tree"/>
            <field name="search_view_id" ref="account.view_account_bill_filter"/>
            <field name="domain">[('move_type', 'in', ['in_invoice', 'in_refund']), ('state', '=', 'posted'), ('amount_residual', '>', 0)]</field>
            <field name="context">{'create': False, 'edit': False}</field>
        </record>

        <menuitem id="bill_outstanding_menu" name="Outstanding Bills" parent="account.menu_finance_payables" sequence="19"
                  action="action_bill_outstanding"/>

        <record id="action_for_generate_ppr" model="ir.actions.server">
            <field name="name">Generate PPR</field>
            <field name="model_id" ref="account.model_account_move"/>
            <field name="binding_model_id" ref="account.model_account_move"/>
            <field name="binding_view_types">list</field>
            <field name="state">code</field>
            <field name="code">
                if records:
                    action = records.action_generate_ppr()
            </field>
        </record>

    </data>
</odoo>
