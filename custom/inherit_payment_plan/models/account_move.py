from odoo import fields, api, models, _
from odoo.exceptions import ValidationError


class AccountMove(models.Model):
    _inherit = 'account.move'

    payment_plan_id = fields.Many2one('account.payment.plan', 'Payment Plan')

    # def _compute_payment_state(self):
    #     for rec in self:
    #         if (self._context.get('pass_validation') and rec.payment_state == 'paid') or rec.move_type == 'entry':
    #             pass
    #         else:
    #             res = super(Account<PERSON>ove, self)._compute_payment_state()
    #             return res

    def action_generate_ppr(self):
        for bill in self:
            if bill.state != 'posted':
                raise ValidationError('Bill must be posted')
            if bill.amount_residual == 0:
                raise ValidationError('Bill already paid')
        return {
            'name': _('Generate PPR Wizard'),
            'type': 'ir.actions.act_window',
            'res_model': 'generate.ppr.wizard',
            'view_mode': 'form',
            'target': 'new',
        }
