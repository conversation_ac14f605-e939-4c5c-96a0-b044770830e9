from odoo import models, fields, api
from odoo.exceptions import UserError, ValidationError
from datetime import datetime




class AccountPaymentPlanLine(models.Model):
    _inherit = "account.payment.plan.line"

    business_units = fields.Char('Business Units')
    payment_method = fields.Char('Payment Method')
    payment_method_line_id = fields.Many2one('account.payment.method.line', string='Payment Method')
    is_process = fields.Boolean(default=True)
    partner_bank_id = fields.Many2one('res.partner.bank', 'Recipient Bank')
    ref = fields.Text('Description', related='payment_plan_id.description', store=True)

    @api.depends('payment_plan_id.pay_groups')
    def _compute_pay_groups(self):
        for rec in self:
            if self.payment_plan_id.pay_groups:
                self.pay_groups = self.payment_plan_id.pay_groups

    @api.model
    def create(self, vals):
        if vals.get('move_id'):
            existing = self.env['account.payment.plan.line'].search([
                ('move_id', '=', vals['move_id']), ('payment_plan_id.state', 'not in', ['cancel', 'rejected'])
            ])
            date_plan = self.env['account.payment.plan'].browse(vals.get('payment_plan_id'))
            if existing:
                total_amount = 0
                for exist in existing:
                    total_amount += exist.amount_to_pay
                if vals.get('amount_to_pay') and vals.get('amount_residual'):
                    if total_amount + vals.get('amount_to_pay') > vals.get('amount_residual'):
                        raise ValidationError("Purchase Order yang sama sudah digunakan di dokumen Payment Plan lain.")
            elif vals.get('payment_date'):
                payment_date = datetime.strptime(vals['payment_date'], "%Y-%m-%d").date()
                if payment_date < date_plan.date:
                    raise ValidationError("Payment Method tidak boleh lebih kecil dari tanggal Payment Plan.")

        return super().create(vals)

    @api.depends('amount_residual', 'amount_to_pay')
    def _compute_remaining_amount(self):
        for rec in self:
            rec.remaining_amount = rec.amount_residual - rec.amount_to_pay
    
    @api.onchange('move_id')
    def onchange_move_id(self):
        if self.move_id:
            journal = False
            for group in self.payment_plan_id.pay_groups_id:
                for bank in group.account_ids:
                    for jrnl in bank.allowed_journal_ids:
                        journal = jrnl
            amount_due = self.move_id.amount_total
            pp_line_exist = self.env['account.payment.plan.line'].search([('move_id', '=', self.move_id.id), ('state', 'not in', ['rejected', 'cancel'])])
            for pp_line in pp_line_exist:
                amount_due -= pp_line.amount_to_pay
                if amount_due > 0:
                    self.account_move_id = self.move_id.id
                    self.bill_date = self.move_id.invoice_date
                    self.partner_id =  self.move_id.partner_id.id,
                    self.payment_reference = self.move_id.payment_reference if self.move_id.payment_reference else '',
                    self.ref = self.payment_plan_id.description if self.payment_plan_id.description else '',
                    self.company_id = self.move_id.company_id.id,
                    self.company_currency_id = self.move_id.company_currency_id.id,
                    self.currency_id = self.move_id.currency_id.id,
                    # self.amount_residual = float(self.move_id.amount_residual),
                    # self.amount_to_pay = float(amount_due),
                    # self.remaining_amount = 0,
                    self.is_editable = True,
                    self.is_process = False,
                    self.journal_id = journal.id if journal else self.env['account.journal'].search([('type', '=', 'bank')], limit=1).id,
                    # self.due_date = self.move_id.invoice_date_due,
                    self.partner_bank_id = self.env['res.partner.bank'].search([('partner_id', '=', self.move_id.partner_id.id)], limit=1).id
                    self.business_units = self.env.user.name
