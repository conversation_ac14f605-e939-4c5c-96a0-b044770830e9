from odoo import _, api, fields, models


class AccountPayGroups(models.Model):
    _name = "account.pay.groups"
    _description = "Account Pay Groups"
    _rec_name = "name"

    name = fields.Char(compute='_compute_name', store=True)
    pay_groups = fields.Selection([
        ('in_house_bni', 'In House (BNI)'),
        ('others', 'Others'),
        ('non_mt100', 'Non MT100'),
        ('digi_pos_bni', 'DigiPos BNI'),
        ('digi_pos_others', 'DigiPos Others'),
        ('advertising_in_house_mandiri', 'Advertising In House (Mandiri)'),
        ('advertising_others_mandiri', 'Advertising Others (Mandiri)')
    ], string='Pay Groups', required=True)
    account_ids = fields.Many2many('account.account', string='Account')
    bank_account_id = fields.Many2one('res.partner.bank', string='Bank Account')
    flag = fields.Selection([
        ('In House', 'In House (BNI)'),
        ('Others', 'Others'),
        ('DigiPos BNI', 'DigiPos BNI'),
        ('DigiPos Others', 'DigiPos Others'),
        ('Advertising Mandiri', 'Advertising In House (Mandiri)'),
        ('Advertising Others (Mandiri)', 'Advertising Others (Mandiri)')
    ], string='Flag')

    @api.depends('pay_groups')
    def _compute_name(self):
        for rec in self:
            if rec.pay_groups:
                rec.name = dict(rec._fields['pay_groups'].selection).get(rec.pay_groups)
            else:
                rec.name = ''

