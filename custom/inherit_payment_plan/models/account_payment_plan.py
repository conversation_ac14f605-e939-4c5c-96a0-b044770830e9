from odoo import models, fields, api
from odoo.exceptions import ValidationError
from datetime import datetime, timedelta
import base64
from io import BytesIO
import ftplib
import os
import paramiko
import csv
import io


class PaymentPlanLine(models.Model):
    _inherit = "account.payment.plan"

    _STATES = [
        ("draft", "Draft"),
        ("pending_approval", "Pending Approval"),
        ("to_approve", "To Be Approved"),
        ("approved", "Approved"),
        ("processed", "Processed"),
        ("error", "Error"),
        ("rejected", "Rejected"),
        ("cancel", "Cancelled"),
    ]

    def default_currency_id(self):
        currency = self.env['res.currency'].search([('name', '=', 'IDR')], limit=1)
        if currency:
            return currency.id
        else:
            return False

    requester_user_by_id = fields.Many2one('res.users', string='Requester By', default=lambda self: self.env.user)
    is_payment = fields.Boolean('Is Payment', default=False)
    approval_payment_plan_ids = fields.One2many('approval.payment.plan', 'plan_id', string='Approval PAyment Plan')
    pay_groups = fields.Selection([
        ('in_house_bni', 'In House (BNI)'),
        ('others', 'Others'),
        ('non_mt100', 'Non MT100'),
        ('digi_pos_bni', 'DigiPos BNI'),
        ('digi_pos_others', 'DigiPos Others'),
        ('advertising_in_house_mandiri', 'Advertising In House (Mandiri)'),
        ('advertising_others_mandiri', 'Advertising Others (Mandiri)')
    ], string='Pay Groups Selection', required=False)
    currency_id = fields.Many2one('res.currency', store=True, tracking=True, string='Currency', default=default_currency_id)
    pay_through_days = fields.Integer('Pay Through Days', required=False)
    pay_through_date = fields.Date('Pay Through Date', required=False)
    date_basis = fields.Selection([
        ('payment_date', 'Payment Date'),
        ('due_date', 'Due Date')
    ], string='Date Basis', default="due_date", readonly=True)
    pay_groups_id = fields.Many2one('account.pay.groups', 'Pay Groups')
    partner_bank_id = fields.Many2one('res.partner.bank', 'Bank')
    state = fields.Selection(selection=_STATES, string="Status", index=True,
                             tracking=True, required=True, copy=False, default="draft", )

    @api.onchange('date', 'pay_through_days')
    def _onchange_pay_through_days(self):
        if self.date:
            self.pay_through_date = self.date + timedelta(days=self.pay_through_days)

    # @api.onchange('pay_through_date')
    # def _onchange_(self):
    #     if self.pay_through_date and self.pay_through_date > self.date:
    #         raise ValidationError('Pay Through Days tidak boleh lebih dari Date')
        
    def action_approve(self):
        if not self.line_ids:
            raise ValidationError('Tambahkan data vendor bill yang akan dibuatkan payment')
        lines = self.line_ids.filtered(lambda x: x.is_process)
        if not lines:
            raise ValidationError('Tidak ada bill yang di process')
        self._create_payment()
        self.state = 'approved'
        self.is_payment = True
        # for line in lines:
        #     move = self.env['account.move'].create([{
        #         'name': '/',
        #         'ref': self.name,
        #         'date': line.payment_date,
        #         'journal_id': line.journal_id.id,
        #         'move_type': 'entry',
        #         'payment_plan_id': self.id,
        #         'line_ids': [
        #             (0, 0, {
        #                 'account_id': line.partner_id.property_account_payable_id.id,
        #                 'debit': line.amount_to_pay,
        #                 'credit': 0.0,
        #                 # 'name': self.journal_desc_time_deposit,
        #             }),
        #             (0, 0, {
        #                 'account_id': line.journal_id.inbound_payment_method_line_ids[0].payment_account_id.id,
        #                 'credit': line.amount_to_pay,
        #                 'debit': 0.0,
        #                 # 'name': self.journal_desc_time_deposit,
        #             })
        #         ]
        #     }])
        #     move.action_post()

        if self.pay_groups_id and self.pay_groups_id.flag in ['In House', 'Others', 'DigiPos BNI', 'DigiPos Others', 'Advertising Mandiri', 'Advertising Others (Mandiri)']:
            mt100_config = self.env['ap.mt100.config'].search([('active', '=', True)], limit=1)
            if not mt100_config:
                raise ValidationError('MT100 Configuration tidak ada')

            output = io.StringIO()
            writer = csv.writer(output, quoting=csv.QUOTE_MINIMAL)

            # file_content = f""
            for line in self.line_ids:
                code = 'MT100'
                ref_number = self.name
                value_date = line.bill_date.strftime('%Y%m%d') if line.bill_date else ''
                currency = line.currency_id.name if self.currency_id else ''
                amount = str(line.amount_to_pay)
                ordering_party_name = ''
                ordering_party_no = line.journal_id.bank_account_id.account_number if line.journal_id and line.journal_id.bank_account_id else ''
                col_8 = ''
                special_rate_code = ''
                rtgs_flag = ''
                col_11 = ''
                col_12 = ''
                col_13 = ''
                bank_code = line.move_id.partner_bank_id.bank_id.bic if line.move_id.partner_bank_id and line.move_id.partner_bank_id.bank_id else ''
                bank_name = line.move_id.partner_bank_id.bank_id.name if line.move_id.partner_bank_id and line.move_id.partner_bank_id.bank_id else ''
                bank_add1 = line.move_id.partner_bank_id.bank_id.city if line.move_id.partner_bank_id and line.move_id.partner_bank_id.bank_id else ''
                bank_add2 = ''
                bene_no = line.move_id.partner_bank_id.account_number if line.move_id.partner_bank_id else ''
                bene_name = line.move_id.partner_id.name
                remark1 = ''
                remark2 = ''
                charges = line.payment_method_line_id.name if line.payment_method_line_id else ''
                email_bene = ''
                # file_content = f"Name: {self.name}\nDate: {self.create_date}"
                # file_content += (f"{code},{ref_number},{value_date},{currency},{amount},{ordering_party_name},{ordering_party_no},"
                #                 f"{col_8},{special_rate_code},{rtgs_flag},{col_11},{col_12},{col_13},{bank_code},{bank_name},"
                #                 f"{bank_add1},{bank_add2},{bene_no},{bene_name},{remark1},{remark2},{charges},{email_bene}\n")
                writer.writerow([code,ref_number,value_date,currency,amount,ordering_party_name,ordering_party_no,
                                col_8,special_rate_code,rtgs_flag,col_11,col_12,col_13,bank_code,bank_name,
                                bank_add1,bank_add2,bene_no,bene_name,remark1,remark2,charges,email_bene])
            csv_data = output.getvalue()
            output.close()
            # filename = f'{self.name}.txt'
            if mt100_config.file_name_patterns_type == 'date':
                date_patterns = [datetime.today().strftime(p) for p in [p.strip() for p in mt100_config.file_name_patterns.split(',') if p.strip()]]
                filename = f'{date_patterns[0]}'
            else:
                filename = f'{mt100_config.file_name_patterns}'

            # Convert to bytes and encode as base64
            file_buffer = BytesIO()
            # file_buffer.write(file_content.encode('utf-8'))
            file_buffer.write(csv_data.encode('utf-8'))
            file_buffer.seek(0)
            # encoded_content = base64.b64encode(file_content.encode('utf-8'))
            encoded_content = base64.b64encode(csv_data.encode('utf-8'))

            # 3. FTP Configuration (you can make these fields or use system params)
            # FTP_HOST = '************'
            FTP_HOST = mt100_config.host
            # FTP_PORT = 21
            FTP_PORT = mt100_config.port
            # FTP_USER = 'ASUS'
            FTP_USER = mt100_config.username
            # FTP_PASS = ''
            FTP_PASS = mt100_config.password
            # FTP_DIR = os.path.join("C:", "Users", "ASUS", "Documents")
            FTP_DIR = mt100_config.get_computed_remote_path()

            try:
                transport = paramiko.Transport((FTP_HOST, FTP_PORT))
                transport.connect(username=FTP_USER, password=FTP_PASS)
                sftp = paramiko.SFTPClient.from_transport(transport)
                sftp.chdir(FTP_DIR)
                sftp.putfo(file_buffer, filename)
                if filename in sftp.listdir():
                    print("Upload sukses!")
                    self.state = 'processed'
                    self.state = 'processed'
                    self.env['ir.attachment'].create({
                        'name': f'{filename}',
                        'type': 'binary',
                        'datas': encoded_content,
                        'res_model': self._name,
                        'res_id': self.id,
                        'mimetype': 'text/plain',
                    })
                    payments = self.env['account.payment'].search([('payment_plan_id', '=', self.id)])
                    for payment in payments:
                        payment.action_post()
                        payment.action_validate()
                    return mt100_config.display_notification('File Transfer',f'File transfer successful!', 'success')
                else:
                    print("Upload gagal!")
                    self.state = 'error'
                    return mt100_config.display_notification('File Transfer',f'File transfer failed!', 'warning')

            except Exception as e:
                self.state = 'error'
                return mt100_config.display_notification('File Transfer', f'FTP upload failed: {str(e)}', 'warning')

    def push_manual_sftp(self):
        if self.pay_groups_id and self.pay_groups_id.flag in ['In House', 'Advertising Mandiri']:
            mt100_config = self.env['ap.mt100.config'].search([('active', '=', True)], limit=1)
            if not mt100_config:
                raise ValidationError('MT100 Configuration tidak ada')

            output = io.StringIO()
            writer = csv.writer(output, quoting=csv.QUOTE_MINIMAL)

            # file_content = f""
            for line in self.line_ids:
                code = 'MT100'
                ref_number = self.name
                value_date = line.bill_date.strftime('%Y%m%d') if line.bill_date else ''
                currency = line.currency_id.name if self.currency_id else ''
                amount = str(line.amount_to_pay)
                ordering_party_name = ''
                ordering_party_no = line.journal_id.bank_account_id.account_number if line.journal_id and line.journal_id.bank_account_id else ''
                col_8 = ''
                special_rate_code = ''
                rtgs_flag = ''
                col_11 = ''
                col_12 = ''
                col_13 = ''
                bank_code = line.move_id.partner_bank_id.bank_id.bic if line.move_id.partner_bank_id and line.move_id.partner_bank_id.bank_id else ''
                bank_name = line.move_id.partner_bank_id.bank_id.name if line.move_id.partner_bank_id and line.move_id.partner_bank_id.bank_id else ''
                bank_add1 = line.move_id.partner_bank_id.bank_id.city if line.move_id.partner_bank_id and line.move_id.partner_bank_id.bank_id else ''
                bank_add2 = ''
                bene_no = line.move_id.partner_bank_id.account_number if line.move_id.partner_bank_id else ''
                bene_name = line.move_id.partner_id.name
                remark1 = ''
                remark2 = ''
                charges = line.payment_method_line_id.name if line.payment_method_line_id else ''
                email_bene = ''
                # file_content = f"Name: {self.name}\nDate: {self.create_date}"
                # file_content += (f"{code},{ref_number},{value_date},{currency},{amount},{ordering_party_name},{ordering_party_no},"
                #                 f"{col_8},{special_rate_code},{rtgs_flag},{col_11},{col_12},{col_13},{bank_code},{bank_name},"
                #                 f"{bank_add1},{bank_add2},{bene_no},{bene_name},{remark1},{remark2},{charges},{email_bene}\n")
                writer.writerow([code,ref_number,value_date,currency,amount,ordering_party_name,ordering_party_no,
                                col_8,special_rate_code,rtgs_flag,col_11,col_12,col_13,bank_code,bank_name,
                                bank_add1,bank_add2,bene_no,bene_name,remark1,remark2,charges,email_bene])
            csv_data = output.getvalue()
            output.close()
            # filename = f'{self.name}.txt'
            if mt100_config.file_name_patterns_type == 'date':
                date_patterns = [datetime.today().strftime(p) for p in [p.strip() for p in mt100_config.file_name_patterns.split(',') if p.strip()]]
                filename = f'{date_patterns[0]}'
            else:
                filename = f'{mt100_config.file_name_patterns}'

            # Convert to bytes and encode as base64
            file_buffer = BytesIO()
            # file_buffer.write(file_content.encode('utf-8'))
            file_buffer.write(io.BytesIO(csv_data.encode('utf-8')))
            file_buffer.seek(0)
            # encoded_content = base64.b64encode(file_content.encode('utf-8'))
            encoded_content = base64.b64encode(io.BytesIO(csv_data.encode('utf-8')))

            # 3. FTP Configuration (you can make these fields or use system params)
            # FTP_HOST = '************'
            FTP_HOST = mt100_config.host
            # FTP_PORT = 21
            FTP_PORT = mt100_config.port
            # FTP_USER = 'ASUS'
            FTP_USER = mt100_config.username
            # FTP_PASS = ''
            FTP_PASS = mt100_config.password
            # FTP_DIR = os.path.join("C:", "Users", "ASUS", "Documents")
            FTP_DIR = mt100_config.get_computed_remote_path()

            try:
                transport = paramiko.Transport((FTP_HOST, FTP_PORT))
                transport.connect(username=FTP_USER, password=FTP_PASS)
                sftp = paramiko.SFTPClient.from_transport(transport)
                sftp.chdir(FTP_DIR)
                sftp.putfo(file_buffer, filename)
                if filename in sftp.listdir():
                    print("Upload sukses!")
                    self.state = 'processed'
                    self.env['ir.attachment'].create({
                        'name': f'{filename}',
                        'type': 'binary',
                        'datas': encoded_content,
                        'res_model': self._name,
                        'res_id': self.id,
                        'mimetype': 'text/plain',
                    })
                    payments = self.env['account.payment'].search([('payment_plan_id', '=', self.id)])
                    for payment in payments:
                        payment.action_post()
                        payment.action_validate()
                    return mt100_config.display_notification('File Transfer',f'File transfer successful!', 'success')
                else:
                    print("Upload gagal!")
                    self.state = 'error'
                    return mt100_config.display_notification('File Transfer',f'File transfer failed!', 'warning')

            except Exception as e:
                self.state = 'error'
                return mt100_config.display_notification('File Transfer', f'FTP upload failed: {str(e)}', 'warning')

    def action_cancel(self):
        self.state = 'cancel'
        payments = self.env['account.payment'].search([('payment_plan_id', '=', self.id)])
        for payment in payments:
            payment.action_cancel()

    def action_draft(self):
        payments = self.env['account.payment'].search([('payment_plan_id', '=', self.id)])
        for payment in payments:
            if payment.state in ['draft', 'in_process', 'paid']:
                raise ValidationError('Tidak bisa cancel Payment Process Request jika masih ada Payment belum di cancel')
        self.state = 'draft'

    @api.depends('selection', 'partner_ids')
    def compute_domain_bill(self):
        for rec in self:
            list_bill = []
            manual_bill = []
            domain = [('move_type', '=', 'in_invoice'), ('state', '=', 'posted'), ('payment_state', '!=', 'paid')]
            # if rec.selection == 'program':
            #     domain.append(('program_id', '=', rec.program_id.id))
            if rec.selection == 'vendor':
                domain.append(('partner_id', 'in', rec.partner_ids.ids))
            for bill in self.env['account.move'].sudo().search(domain):
                list_bill.append(bill.id)
            for bill_manual in self.env['account.move'].sudo().search(domain):
                manual_bill.append(bill_manual.id)
            if rec.selection != 'manual':
                rec.domain_bill_ids = [(6, 0, list_bill)]
            else:
                domain.extend([
                    ('source', '=', 'manual'),
                    ('validation_state', '=', 'approve')
                ])
                rec.domain_bill_ids = [(6, 0, manual_bill)]

    def _create_payment(self):
        partner_payments = {}
        for line in self.line_ids:

            if line.is_process:
                partner_id = line.partner_id.id
                # if partner_id in partner_payments:
                #     partner_payments[partner_id]['amount'] += line.amount_to_pay
                # else:
                payment_vals = {
                    'payment_plan_id': self.id,
                    'date': line.payment_date,
                    'amount': line.amount_to_pay,
                    'payment_type': 'outbound',
                    'partner_type': 'supplier',
                    # 'ref': line.ref,
                    'journal_id': line.journal_id.id,
                    'currency_id': line.currency_id.id,
                    'partner_id': partner_id,
                    'company_id': line.move_id.company_id.id,
                    'payment_method_line_id': line.payment_method_line_id.id,
                    'is_plan_failed': True
                }
                    # partner_payments[partner_id] = payment_vals

                default_date = fields.Date.today()
                # for l in self.line_ids:
                #     if l.is_process:
                #         default_date = l.payment_date or default_date
                #         break

                payment = self.env['account.payment'].with_context({
                    'default_company_id': self.company_id.id,
                    'default_move_journal_types': ['bank'],
                    'create_from_payment_plan': True,
                    'default_date': line.payment_date if line.payment_date else default_date,
                })

                # for partner_id, partner_payment_vals in partner_payments.items():
                payment_id = payment.sudo().create(payment_vals)  # ← diperbaiki

                if self.selection != 'manual':
                    # for l in self.line_ids:
                    #     if l.is_process and l.partner_id.id == payment_id.partner_id.id:
                    bill = self.env['account.payment.invoice'].sudo().create({
                        'payment_id': payment_id.id,
                        'move_id': line.move_id.id
                    })
                    bill._onchange_set_values()

                account = False
                # for line in self.line_ids.filtered(lambda x: x.is_process):
                line_credit = line.move_id.line_ids.filtered(lambda x: x.credit > 0 and x.account_id.account_type in ['liability_payable', 'asset_receivable'])
                if line_credit:
                    account = line_credit.account_id.id
                if account:
                    for line in payment_id.move_id.line_ids:
                        if line.debit > 0:
                            line.write({'account_id': account})

    @api.onchange('partner_ids')
    def _onchange_partner(self):
        line_data = []
        bill = self.env['account.move'].search([('move_type', '=', 'in_invoice'), ('payment_state', '!=', 'paid'),
                                                ('state', '=', 'posted'), ('partner_id', 'in', self.partner_ids.ids)])
        journal = False
        for group in self.pay_groups_id:
            for bank in group.account_ids:
                for jrnl in bank.allowed_journal_ids:
                    journal = jrnl

        list_group = []
        group_data = []

        self.line_ids = [(6, 0, [])]
        for rec in bill:
            rec._compute_payments_widget_reconciled_info()
            if rec.payment_state != 'paid':
                amount_due = rec.amount_total
                pp_line_exist = self.env['account.payment.plan.line'].search([('move_id', '=', rec.id), ('state', 'not in', ['rejected', 'cancel'])])
                for pp_line in pp_line_exist:
                    amount_due -= pp_line.amount_to_pay
                # if rec.payment_group not in list_group:
                #     list_group.append(rec.payment_group)
                if amount_due > 0:
                    line_data.append((0, 0, {
                        'account_move_id': rec.id,
                        'move_id': rec.with_context({'show_view_search_bill_no': True}).id,
                        'bill_date': rec.invoice_date,
                        # 'bill_type': rec.bill_type,
                        'partner_id': rec.partner_id,
                        'payment_reference': rec.payment_reference,
                        'ref': rec.ref,
                        'company_id': rec.company_id,
                        'company_currency_id': rec.company_currency_id,
                        'currency_id': rec.currency_id,
                        'amount_residual': rec.amount_residual,
                        'amount_to_pay': amount_due,
                        'remaining_amount': 0,
                        'is_editable': True,
                        'is_process': False,
                        'journal_id': journal.id if journal else self.env['account.journal'].search([('type', '=', 'bank')], limit=1).id,
                        'due_date': rec.invoice_date_due,
                        'partner_bank_id': rec.partner_bank_id.id if rec.partner_bank_id else False,
                        # 'payment_group': rec.payment_group
                    }))

        self.line_ids = line_data
        self.group_plan_ids = [(6, 0, [])]
        # for group in list_group:
        #     group_data.append((0, 0, {
        #         'payment_group': group,
        #         'payment_plan_id': rec.id
        #     }))
        # self.group_plan_ids = group_data

    @api.onchange('selection')
    def onchange_selection(self):
        if self.selection == 'manual':
            self.line_ids = [(5, 0, 0)]

    def unlink(self):
        for rec in self:
            if rec.state in ['pending_approval', 'to_approve', 'processed', 'approved']:
                raise ValidationError('Delete on Draft State only')
        return super(PaymentPlanLine, self).unlink()

    def get_journal_entries(self):
        self.ensure_one()
        return {
            'type': 'ir.actions.act_window',
            'name': 'Journal Entries',
            'view_mode': 'list,form',
            'res_model': 'account.move',
            'domain': [('payment_plan_id', '=', self.id)],
            'context': "{'create': False}"
        }
