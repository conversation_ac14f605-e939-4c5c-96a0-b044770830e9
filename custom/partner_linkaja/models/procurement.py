from odoo import models, fields, api
from datetime import datetime


class ProcurmentUpdateData(models.Model):
    _name = "procurement.update.data"
    _description = "Procurment Type"
    
    procurement_state = fields.Selection([ 
        ('draft', 'Draft'),
        ('pending_approval', 'Pending Approval'),
        ('approved', 'Approved'),
    ], string="Status", default='draft')
    street2 = fields.Char(string="Alamat2")
    zip = fields.Char(string="Kode Pos")
    city = fields.Char(string="Kota")
    state_id = fields.Many2one('res.country.state', string='Provinsi')
    country_id = fields.Many2one('res.country', string='Negara')
    
    def action_save(self):
        return True

    def action_discard(self):
        return True

    def action_pending_approval(self):  
        self.write({'procurement_state': 'pending_approval'})

    def action_approve(self):
        self.write({'procurement_state': 'approved'})

    def action_draft(self):
        self.write({'procurement_state': 'draft'})
    
    name = fields.Char(string='Name')
    partner_id = fields.Many2one('res.partner', string='Vendor', ondelete='cascade')
    general_info_checked = fields.Boolean(string="General Information ")
    company_name = fields.Char(string="Company")
    company_address = fields.Char(string="Address")
    bank_account_checked = fields.Boolean(string="Bank Account ")
    tax_status_checked = fields.Boolean(string="Tax Status ") 
    tax_document = fields.Selection([
        ('ktp', 'KTP'),
        ('npwp', 'NPWP'),
        ('others', 'Others (ex. COR / DGT)'),
    ], string="Tax Document")
    tax_organization_type = fields.Many2one('tax.organization.type', string="Tax Organization Type")
    npwp_number = fields.Char(string="Tax Registration Number")
    ktp_number = fields.Char(string="KTP Number")
    cor_number = fields.Char(string="COR/DGT Number")
    term_payment_checked = fields.Boolean(string="Term of Payment")
    payment_term_id = fields.Many2one('account.payment.term', string="Term of Payment")
    contact_ids = fields.One2many('procurement.contact', 'procurement_id', string='Contacts')
    bank_procurement_ids = fields.One2many('procurement.bank', 'procurement_id', string='Bank Accounts')
    bank_ids = fields.One2many('res.partner.bank', 'partner_id', string='Bank Accounts')
    approval_ids = fields.One2many('procurement.approval.line', 'procurement_id', string="Approvals")
    sequence = fields.Integer(string='No', compute='_compute_sequence', store=False)
    reference_number = fields.Char(string='Reference Number', readonly=True, copy=False,)
    
    
    @api.model
    def create(self, vals):
        if vals.get('reference_number', 'New') == 'New':
            vals['reference_number'] = self.env['ir.sequence'].next_by_code('procurement.update.data') or '/'
        return super(ProcurmentUpdateData, self).create(vals)


    @api.depends('sequence')
    def _compute_sequence(self):
        for idx, rec in enumerate(self.sorted(key=lambda r: r.create_date), start=1):
            rec.sequence = idx
    
    
        
    def action_approve(self):
        for record in self:
            record.procurement_state = 'approved' 
            record._update_partner_data()
    
    @api.onchange('partner_id')
    def _onchange_partner_id(self):
        for rec in self:
            partner = rec.partner_id
            if partner:
                rec.company_name = partner.name
                rec.company_address = partner.street
                rec.street2 = partner.street2
                rec.zip = partner.zip
                rec.city = partner.city
                rec.state_id = partner.state_id
                rec.country_id = partner.country_id
                rec.tax_document = partner.tax_document
                rec.tax_organization_type = partner.tax_organization_type
                rec.npwp_number = partner.npwp_number
                rec.ktp_number = partner.ktp_number
                rec.cor_number = partner.cor_number
                rec.payment_term_id = partner.payment_term_id
                
                rec.contact_ids = [
                    (0, 0, {
                        'name': contact.name,
                        'email': contact.email,
                        'phone': contact.phone,
                        'mobile': contact.mobile,
                        'street': contact.street,
                        'street2': contact.street2,
                        'state_id': contact.state_id,
                        'zip': contact.zip,
                        'country_id': contact.country_id,
                        'type': contact.type,
                        'title': contact.title.id if contact.title else False,
                        'function': contact.function,
                        'is_bidding': contact.is_bidding,
                        'partner_pic_id': contact.id
                    }) for contact in partner.child_ids 
                    if contact.type == 'contact' or contact.type == 'other' 
                    or contact.type == 'invoice' or contact.type == 'delivery' or contact.type == 'followup'
                ]

                rec.bank_procurement_ids = [
                    (0, 0, {
                        'acc_number': bank_partner.acc_number,
                        'bank_id': bank_partner.bank_id,
                        'allow_out_payment': bank_partner.allow_out_payment,
                        'acc_holder_name': bank_partner.acc_holder_name,
                        'tipe_rekening_bank': bank_partner.tipe_rekening_bank,
                        'account_holder': partner.name,
                        'account_number': bank_partner.account_number
                    }) for bank_partner in partner.bank_ids
                ]

                

    def _update_partner_data(self):
        for record in self:
            if record.partner_id:
                updates = {}
                if record.company_name and record.partner_id.name != record.company_name:
                    updates['name'] = record.company_name
                if record.company_address and record.partner_id.contact_address != record.company_address:
                    updates['street'] = record.company_address 
                    
                if record.tax_document != record.partner_id.tax_document:
                    updates['tax_document'] = record.tax_document
                if record.tax_organization_type != record.partner_id.tax_organization_type:
                    updates['tax_organization_type'] = record.tax_organization_type
                if record.npwp_number and record.npwp_number != record.partner_id.npwp_number:
                    updates['npwp_number'] = record.npwp_number
                if record.ktp_number and record.ktp_number != record.partner_id.ktp_number:
                    updates['ktp_number'] = record.ktp_number
                if record.cor_number and record.cor_number != record.partner_id.cor_number:
                    updates['cor_number'] = record.cor_number
                if record.payment_term_id != record.partner_id.payment_term_id:
                    updates['payment_term_id'] = record.payment_term_id
                    
                if updates:
                    record.partner_id.write(updates)

                # new update for contacts
                create_new_contact = []
                for contact in record.contact_ids:
                    vals = {
                        'name': contact.name,
                        'email': contact.email,
                        'phone': contact.phone,
                        'mobile': contact.mobile,
                        'street': contact.street,
                        'street2': contact.street2,
                        'state_id': contact.state_id.id if contact.state_id else False,
                        'zip': contact.zip,
                        'city': contact.city,
                        'country_id': contact.country_id.id if contact.country_id else False,
                        'type': contact.type,
                        'title': contact.title.id if contact.title else False,
                        'function': contact.function,
                        'is_bidding': contact.is_bidding
                    }
                    if contact.partner_pic_id:
                        contact.partner_pic_id.write(vals)
                    else:
                        create_new_contact.append((0, 0, vals))

                if create_new_contact:
                    record.partner_id.child_ids = create_new_contact                    
                                
                existing_banks = {b.acc_number: b for b in record.partner_id.bank_ids}
                bank_vals = []

                existing_acc_numbers = [bank.acc_number for bank in record.bank_procurement_ids]
                for acc_number, bank in existing_banks.items():
                    if acc_number not in existing_acc_numbers:
                        bank_vals.append((2, bank.id, 0))

                for bank in record.bank_procurement_ids:
                    if bank.acc_number in existing_banks:

                        bank_vals.append((1, existing_banks[bank.acc_number].id, {
                            'bank_id': bank.bank_id.id if bank.bank_id else False,
                            'allow_out_payment': bank.allow_out_payment,
                            'acc_holder_name': bank.acc_holder_name,
                            'tipe_rekening_bank': bank.tipe_rekening_bank.id if bank.tipe_rekening_bank else False,
                            'account_number': bank.account_number,
                        }))
                    else:

                        bank_vals.append((0, 0, {
                            'acc_number': bank.acc_number,
                            'bank_id': bank.bank_id.id if bank.bank_id else False,
                            'allow_out_payment': bank.allow_out_payment,
                            'acc_holder_name': bank.acc_holder_name,
                            'tipe_rekening_bank': bank.tipe_rekening_bank.id if bank.tipe_rekening_bank else False,
                            'account_number': bank.account_number,
                        }))

                record.partner_id.write({'bank_ids': bank_vals})
    
    
class ProcurementContact(models.Model):
    _name = 'procurement.contact'
    _description = 'Procurement Contact'

    procurement_id = fields.Many2one('procurement.update.data', string='Procurement Reference', ondelete='cascade')
    partner_pic_id = fields.Many2one('res.partner', string='Partner PIC')
    contact_name = fields.Char(string="Contact Name")
    contact_email = fields.Char(string="Email")
    contact_number = fields.Char(string="Phone")
    
    name = fields.Char(string="Contact Name")
    name_co = fields.Char(string="Contact Name")
    title = fields.Many2one('res.partner.title', string='Title')
    function = fields.Char(string='Function')
    email = fields.Char(string='Email')
    phone = fields.Char(string='Phone')
    mobile = fields.Char(string='Mobile')
    type = fields.Selection([
        ('contact', 'Contact'),
        ('invoice', 'Invoice Address'),
        ('delivery', 'Delivery Address'),
        ('followup', 'Follow-up Address'),
        ('other', 'Alamat NPWP')
    ], string="Address Type", default='other')

    street = fields.Char(string="Street")
    street2 = fields.Char(string="Street2")
    city = fields.Char(string="City")
    state_id = fields.Many2one("res.country.state", string='State')
    zip = fields.Char(string="Zip")
    country_id = fields.Many2one('res.country', string='Country')
    lang = fields.Selection(selection=lambda self: self.env['res.lang'].get_installed(), string='Language')
    comment = fields.Text(string="Internal Notes")

    avatar_128 = fields.Binary("Avatar")
    color = fields.Integer("Color")
    is_company = fields.Boolean("Is a Company")

    is_bidding = fields.Boolean(
        string="Is Bidding",
        help="Indicates whether the partner is involved in bidding.",
        copy=False
    )
    
    
    
    
class ProcurementBank(models.Model):
    _name = 'procurement.bank'
    _description = 'Procurement Bank Account'

    procurement_id = fields.Many2one('procurement.update.data', string='Procurement Reference', ondelete='cascade')
    bank = fields.Char(string='Bank')
    account_number = fields.Char(string='Account Number')
    account_type = fields.Selection([
        ('bni_inhouse', 'BNI Inhouse'),
        ('others', 'Others'),
    ], string='Type')
    country_id = fields.Many2one('res.country', string='Country')
    branch = fields.Char(string='Branch')

    sequence = fields.Integer(string="Sequence", help="Sequence of the bank account")
    acc_number = fields.Char(string="Bank account", required=True, help="Bank account number")
    bank_id = fields.Many2one('res.bank', string="Bank", required=True, help="Bank where the account is held")
    account_holder = fields.Char(string='Account Holder',  required=True,)
    allow_out_payment = fields.Boolean(string="Send Money", help="Allow outgoing payments from this bank account")
    acc_holder_name = fields.Char(string="Account Holder Name", help="The name of the account holder")
    tipe_rekening_bank = fields.Many2one('tipe.rekening.bank', string='Tipe Rekening Bank')
    _sql_constraints = [
        ('unique_account', 'unique(acc_number, partner_id)', 'The account number must be unique for each partner!')
    ]
    currency_id = fields.Many2one(
        'res.currency',
        string='Currency',
        help='Currency of the bank account'
    )
    is_sub_account = fields.Boolean(string='Is Sub Account', default=False, tracking=True)
    main_partner_bank_id = fields.Many2one('res.partner.bank', domain="[('is_sub_account','=',False)]", string='Main Bank Account', tracking=True)
    main_account_number = fields.Char(string='Main Account Number', related='main_partner_bank_id.account_number', store=True)
    branch_name = fields.Char(string='Branch Name', tracking=True)
    branch_code = fields.Char(string='Branch Code', tracking=True)
    swift_code = fields.Char(string='Swift Code')
    account_number = fields.Char(string='Account Number', tracking=True)
    bank_bic = fields.Char(string="SWIFT Code", related='bank_id.bic', readonly=False)
    
    
class ProcurementApprovalLine(models.Model):
    _name = 'procurement.approval.line'
    _description = 'Procurement Approval Line'
    
    
    procurement_id = fields.Many2one('procurement.update.data', string='Procurement Reference', ondelete='cascade')
    sequence = fields.Integer(string="Sequence")
    approval_name = fields.Char(string="Approval Name")
    approval_position = fields.Char(string="Approval Position")
    approval_status = fields.Selection([
        ('submitted', 'Submitted'),
        ('approved', 'Approved'),
    ], string="Approval Status")
    approval_date = fields.Date(string="Approval Date")
    approval_note = fields.Text(string="Approval Note")
    reassign_to = fields.Many2one('res.users', string="Reassign To")
    reassign_status = fields.Selection([
        ('submitted', 'Submitted'),
        ('approved', 'Approved'),
    ], string="Reassign Status")
    reassign_date = fields.Date(string="Reassign Date")
    reassign_note = fields.Text(string="Reassign Note")
    
    
