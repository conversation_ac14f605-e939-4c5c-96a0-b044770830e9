# -*- coding: utf-8 -*-
"""
AR Adjustment Models

This module contains the models for Account Receivable Adjustments.
It allows creating adjustments to invoices without creating credit notes.
"""
from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError
from odoo.tools import float_is_zero


class Adjustment(models.Model):
    """
    Account Receivable Adjustment model

    This model represents an adjustment to an account receivable invoice.
    It can be used to increase or decrease the amount of an invoice without
    creating a credit note.
    """
    _name = 'ar.adjustment'
    _description = 'Account Receivable Adjustment'
    _inherits = {'account.move': 'move_id'}
    _order = "date desc, name desc"
    _check_company_auto = True

    def _default_journal_id(self):
        domain = [
            *self.env['account.journal']._check_company_domain(self.env.company),
            ('type', '=', 'sale'),
        ]
        journal = self.env['account.journal'].search(domain, limit=1)
        return journal

    move_id = fields.Many2one(
        'account.move', string='Journal Entry', copy=False, index=True,
        readonly=True, required=True, ondelete='cascade',
        check_company=True)
    adj_name = fields.Char(
        string="Reference Number", copy=False, index=True,
        readonly=True)
    description = fields.Char(
        string="Description", copy=False)
    journal_id = fields.Many2one(
        'account.journal', string='AR Adjustment Journal',
        copy=False, check_company=True, index=True,
        default=_default_journal_id)
    company_id = fields.Many2one(
        'res.company', string='Company', store=True, readonly=True,
        related='journal_id.company_id', change_default=True,
        default=lambda self: self.env.company)
    transaction_date = fields.Date(
        'Transaction Date', required=True, copy=False,
        default=fields.Date.today)
    partner_id = fields.Many2one(
        'res.partner', string="Customer",
        store=True, readonly=False, ondelete='restrict', copy=False,
        domain="[('parent_id','=', False), ('state','=', 'active')]")
    invoice_id = fields.Many2one(
        'account.move', string='Invoice',
        copy=False, domain="[('partner_id', '=', partner_id),('move_type', '=', 'out_invoice'),"
                           "('amount_residual', '>', 0), ('state','=', 'posted')]")
    currency_id = fields.Many2one(
        'res.currency', string='Currency', store=True, readonly=False,
        default = lambda self: self.env.company.currency_id,
        copy=False,
        help="The payment's currency.")
    amount_residual_signed = fields.Monetary(
        currency_field='currency_id', default=0.0,
        string="Total residual signed", related='invoice_id.amount_residual_signed',
        compute='_compute_amount', copy=False)
    type_adjustment = fields.Selection(selection=[
        ('deduction', 'Deduction'),
        ('additional', 'Additional')],
        string='Adjustment Type',
        default='deduction',
        readonly=True,
        copy=False)
    total_amount = fields.Monetary(
        currency_field='currency_id', default=0.0,
        string="Total Amount",
        compute='_compute_amount', copy=False)
    adjustment_line_ids = fields.One2many(
        'ar.adjustment.line', 'ar_adjustment_id',
        string='Adjustment Lines', copy=False,
        help="Adjustment Lines.")
    from_invoice = fields.Boolean(string='From Invoice', default=False)

    ### Add Period ###
    period_id = fields.Many2one(
        "invoicing.period.line", string="Period", compute="_compute_get_period", store=True
    )

    ### Add Period #
    @api.depends("transaction_date")
    def _compute_get_period(self):
        if self:
            for rec in self:
                rec.period_id = False
                if rec.transaction_date:
                    period = (
                        self.env["invoicing.period.line"]
                        .sudo()
                        .search(
                            [
                                ("date_start", "<=", rec.transaction_date),
                                ("date_end", ">=", rec.transaction_date),
                                ('move_type', '=', 'out_invoice'),                                
                            ],
                            limit=1,
                        )
                    )
                    if period:
                        rec.period_id = period.id
                        
    @api.depends('adjustment_line_ids.amount')
    def _compute_amount(self):
        for rec in self:
            rec.total_amount = 0
            for pay in rec.adjustment_line_ids:
                if pay.amount:
                    rec.total_amount += pay.amount

    @api.onchange('partner_id')
    def _onchange_partner_id(self):
        if self.partner_id != self.invoice_id.partner_id:
            self.invoice_id = False

    @api.onchange('invoice_id')
    def onchange_invoice(self):
        self.adjustment_line_ids = [(5, 0, 0)]

    @api.model_create_multi
    def create(self, vals_list):
        """
        Override create method to check for closed period and generate journal lines.
        """
        for vals in vals_list:
            # Extract the date to check
            open_date = vals.get('transaction_date') or fields.Date.context_today(self)

            # Period validation based on invoice period (custom model: invoicing.period.line)
            period = self.env['invoicing.period.line'].sudo().search([
                ('date_start', '<=', open_date),
                ('date_end', '>=', open_date),
                ('move_type', '=', 'out_invoice'),
            ], limit=1)

            if period and period.state == 'close':
                raise UserError(_("You cannot create a record on a closed invoice period (Date: %s).") % open_date)

        # Proceed with normal create
        res = super().create(vals_list)

        for i, pay in enumerate(res):
            to_write = {'from_adjustment': True}
            for k, v in vals_list[i].items():
                if k in self._fields and self._fields[k].store and k in pay.move_id._fields and pay.move_id._fields[k].store:
                    to_write[k] = v

            if 'line_ids' not in vals_list[i]:
                to_write['line_ids'] = [(0, 0, line_vals) for line_vals in pay._prepare_move_line_default_vals()]

            pay.move_id.write(to_write)

        return res


    # @api.model_create_multi
    # def create(self, vals):
        # """
        # Override create method to generate sequence number

        # Args:
            # vals (dict): Values for creating the record

        # Returns:
            # Adjustment: Created record
        # """
        # res = super().create(vals)
        # for i, pay in enumerate(res):
            # to_write = {'from_adjustment': True}
            # for k, v in vals[i].items():
                # if k in self._fields and self._fields[k].store and k in pay.move_id._fields \
                        # and pay.move_id._fields[k].store:
                    # to_write[k] = v

            # if 'line_ids' not in vals[i]:
                # to_write['line_ids'] = [(0, 0, line_vals) for line_vals in
                                        # pay._prepare_move_line_default_vals()]
            # pay.move_id.write(to_write)
        # return res

    def action_post(self):
        """ Change state from draft to posted and post the associated journal entry with period validation """
        for record in self:
            # Business logic: prevent over-adjustment
            if record.invoice_id.adjustment_amount + record.total_amount > record.invoice_id.amount_residual:
                raise UserError(_("Adjustment amount cannot be greater than the invoice residual amount!"))

            # Determine posting date
            posting_date = record.date or record.move_id.date

            # Period validation
            period = self.env['invoicing.period.line'].sudo().search([
                ('date_start', '<=', posting_date),
                ('date_end', '>=', posting_date),
                ('move_type', '=', 'out_invoice'),  # Adjust based on your use case
            ], limit=1)

            if period and period.state == 'close':
                raise UserError(
                    _("You cannot post this adjustment because the posting date %s is in a closed invoice period.") % posting_date
                )

            # Post the journal entry
            record.move_id.action_post()

    # def action_post(self):
        # """ Change state from draft to posted and post the associated journal entry """
        # if self.invoice_id.adjustment_amount + self.total_amount > self.invoice_id.amount_residual:
            # raise UserError(_("Adjustment amount cannot be greater than Invoice amount!"))

        # self.move_id.action_post()

    def action_cancel(self):
        """ Change state from posted to cancel and cancel the associated journal entry """
        self.move_id.button_cancel()

    def _prepare_move_line_default_vals(self):
        ''' Prepare the dictionary to create the default account.move.lines for the current payment.
        :param write_off_line_vals: Optional dictionary to create a write-off account.move.line easily containing:
            * amount:       The amount to be added to the counterpart amount.
            * name:         The label to set on the line.
            * account_id:   The account on which create the write-off.
        :return: A list of python dictionary to be passed to the account.move.line's 'create' method.
        '''
        liquidity_line_name = _('Adjustment Account Receivable %s', self.invoice_id.name)
        # find line move on journal and create counterpart journal
        line_vals_list = []
        # Payment Check has not been Sent with statment not yet reconcile and dp has not been paid
        for rec in self.adjustment_line_ids:
            if rec:
                if not rec.counterpart_account_id:
                    raise UserError('Counterpart account is required')

                line_vals_list.append(
                    {
                        'name': liquidity_line_name,
                        'date_maturity': self.transaction_date,
                        # 'amount_currency': rec.amount,
                        'currency_id': self.currency_id.id,
                        'debit': rec.amount if self.type_adjustment == 'deduction' else 0.0,
                        'credit': rec.amount if self.type_adjustment == 'additional' else 0.0,
                        'partner_id': self.partner_id.id or self.journal_id.company_id.partner_id.id,
                        'account_id': rec.account_id.id,
                    }, )
                # Counterpart Account
                line_vals_list.append(
                    {
                        'name': liquidity_line_name,
                        'date_maturity': self.transaction_date,
                        # 'amount_currency': rec.amount,
                        'currency_id': self.currency_id.id,
                        'debit': rec.amount if self.type_adjustment == 'additional' else 0.0,
                        'credit': rec.amount if self.type_adjustment == 'deduction' else 0.0,
                        'partner_id': self.partner_id.id or self.journal_id.company_id.partner_id.id,
                        'account_id': rec.counterpart_account_id.id,
                    }, )

        return line_vals_list

    def _synchronize_to_moves(self, changed_fields):
        print(changed_fields, 'ccccccccc')
        ''' Synchronize the changes made in the adjustment to the linked journal entry '''
        if not changed_fields:
            return

        if self.move_id and self.move_id.state == 'draft':
            self.move_id.journal_id = self.journal_id.id
            self.move_id.date = self.transaction_date
            self.move_id.line_ids = [(5, 0, 0)]
            self.move_id.line_ids = [(0, 0, line_vals) for line_vals in
                                        self._prepare_move_line_default_vals()]

    # def write(self, vals):
        # """ Override write method to synchronize changes to the linked journal entry """
        # res = super().write(vals)
        # self._synchronize_to_moves(set(vals.keys()))
        # return res

    def write(self, vals):
        """ Override write method to validate period and synchronize journal entry """
        for record in self:
            new_date = vals.get('transaction_date', record.date)
            
            period = self.env['invoicing.period.line'].sudo().search([
                ('date_start', '<=', new_date),
                ('date_end', '>=', new_date),
                ('move_type', '=', 'out_invoice'),  # Adjust this if you're using a different move type
            ], limit=1)

            if period and period.state == 'close':
                raise UserError(_("You cannot modify this record with a date that falls in a closed period (%s).") % new_date)

        res = super().write(vals)
        self._synchronize_to_moves(set(vals.keys()))
        return res

    @api.constrains('invoice_id')
    def _check_invoice_period(self):
        for adjustment in self:
            adjustment.invoice_id._check_period()


class AdjustmentLines(models.Model):
    """
    Account Receivable Adjustment Line model

    This model represents the individual adjustment lines for an AR Adjustment.
    Each line can be associated with an invoice line or tax line and contains
    the adjustment amount.
    """
    _name = 'ar.adjustment.line'
    _description = 'Account Receivable Adjustment Line'
    _check_company_auto = True

    ar_adjustment_id = fields.Many2one(
        'ar.adjustment', string='AR Adjustment', copy=False)
    date = fields.Date(related='ar_adjustment_id.transaction_date', string='Date')
    state = fields.Selection(
        selection=[
            ('draft', 'Draft'),
            ('posted', 'Posted'),
            ('cancel', 'Cancelled'),
        ],
        string='Status',
        related='ar_adjustment_id.state',
        required=True,
        readonly=True,
        copy=False,
        tracking=True,
        default='draft')
    invoice_id = fields.Many2one(
        'account.move', related='ar_adjustment_id.invoice_id',
        copy=False)
    line_type = fields.Selection(selection=[
        ('tax', 'Invoice Tax'),
        ('line', 'Invoice Line')],
        string='Line Type', copy=False)
    invoice_line_id = fields.Many2one(
        'account.move.line',
        domain="[('move_id','=', invoice_id), ('price_unit', '>', 0)]",
        string='Invoice Line', copy=False)
    currency_id = fields.Many2one(
        'res.currency', string='Currency', store=True, readonly=False,
        related='invoice_line_id.currency_id', copy=False,
        help="The payment's currency.")
    invoice_amount = fields.Monetary(
        currency_field='currency_id',
        default=0.0, string="Amount")
    amount = fields.Monetary(
        currency_field='currency_id', default=0.0,
        string="Adj Amount")
    account_id = fields.Many2one('account.account', string="Account (Debit)")
    counterpart_account_id = fields.Many2one(
        'account.account',
        string="Counterpart (Credit)")
    company_id = fields.Many2one(
        'res.company', string='Company', store=True, readonly=True,
        related='ar_adjustment_id.company_id', change_default=True,
        default=lambda self: self.env.company)
    move_line_id = fields.Many2one(
        'account.move.line', string='Journal Item', copy=False)

    def name_get(self):
        """
        Override name_get method to display a custom name for adjustment lines

        Returns:
            list: List of tuples with id and name
        """
        result = []
        for record in self:
            name = f"{record.ar_adjustment_id.name or ''} - {record.amount}"
            result.append((record.id, name))
        return result

    @api.onchange('invoice_line_id', 'line_type')
    def _onchange_invoice_line_id(self):
        """
        Update account and invoice amount when invoice line changes
        """
        if self.invoice_line_id and self.line_type == 'line':
            self.account_id = self.invoice_line_id.account_id.id
            self.invoice_amount = self.invoice_line_id.price_total
            self.counterpart_account_id = self.invoice_line_id.partner_id.property_account_receivable_id.id
        elif self.invoice_line_id and self.line_type == 'tax':
            tax_amount = self.invoice_line_id.price_total - self.invoice_line_id.price_subtotal
            tax_line = self.invoice_id.line_ids.filtered(lambda l: l.tax_line_id.id in self.invoice_line_id.tax_ids.ids)
            if not float_is_zero(tax_amount, precision_rounding=self.currency_id.rounding) and tax_line:
                self.account_id = tax_line[0].account_id.id
                self.counterpart_account_id = self.invoice_line_id.partner_id.property_account_receivable_id.id
                self.invoice_amount = tax_amount

    @api.onchange('amount')
    def _onchange_amount(self):
        """
        Validate amount when it changes
        """
        if self.amount and self.amount < 0:
            raise UserError(_("Adjustment amount cannot be negative."))
        elif self.amount > self.invoice_amount:
            raise UserError(_("Adjustment amount cannot be greater than the Invoice amount!"))

    @api.constrains('invoice_amount', 'amount')
    def _check_amount_limit(self):
        for adjustment in self:
            if adjustment.amount > adjustment.invoice_amount:
                raise UserError(_("Adjustment amount cannot be greater than Invoice amount!"))
