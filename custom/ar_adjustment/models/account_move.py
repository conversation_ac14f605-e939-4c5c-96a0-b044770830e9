# -*- coding: utf-8 -*-
"""
Account Move Extensions for AR Adjustment

This module extends the account.move model to add AR Adjustment functionality.
It adds fields and methods to track and compute adjustment amounts.
"""
from odoo import api, fields, models, _
from odoo.tools import float_is_zero


class AccountMove(models.Model):
    """
    Extension of account.move model for AR Adjustment

    Adds fields and methods to track and compute adjustment amounts.
    """
    _inherit = 'account.move'

    # AR Adjustment fields
    adjustment_ids = fields.One2many(
        'ar.adjustment', 'invoice_id',
        string='Adjustment Invoices')
    adjustment_amount = fields.Monetary(
        string='Adjustment Amount', store=True,
        readonly=True, tracking=True,
        compute='_compute_adjustment_amount')
    from_adjustment = fields.Boolean(
        string='Journal Entry from AR Adjustment',
        default=False, copy=False)

    def _create_adjustment_move_sequence(self):
        vals = {
            'name': 'Adjustment Account move',
            'code': 'account.move.adjustment',
            'prefix': 'INV/ADJ/%(year)s/',
            'padding': 5,
        }
        return self.env['ir.sequence'].create(vals)

    def action_post(self):
        for move in self:
            if not move.name or move.name == '/' and move.from_adjustment:
                sequence = self.env['ir.sequence'].search([('code', '=', 'account.move.adjustment')], limit=1)
                if not sequence:
                    sequence = self._create_adjustment_move_sequence()

                move.name = sequence._next()
        return super().action_post()

    @api.depends('adjustment_ids.adjustment_amount', 'adjustment_ids.state')
    def _compute_adjustment_amount(self):
        for move in self:
            move.adjustment_amount = 0
            for adjustment in move.adjustment_ids:
                if adjustment.state != 'posted' or adjustment.total_amount <= 0:
                    continue

                if adjustment.type_adjustment == 'additional':
                    move.adjustment_amount += adjustment.total_amount
                elif adjustment.type_adjustment == 'deduction':
                    move.adjustment_amount += (-1 * adjustment.total_amount)

    @api.depends('adjustment_amount') 
    def _compute_amount(self):
        super()._compute_amount()
        for move in self:
            if not float_is_zero(move.adjustment_amount, precision_rounding=move.currency_id.rounding):
                move.amount_residual += move.adjustment_amount
                move.amount_residual_signed += move.adjustment_amount

    def action_create_ar_adjustment(self):
        """
        Action to create a new AR Adjustment from the invoice

        Returns:
            dict: Action dictionary to open AR Adjustment form view
        """
        self.ensure_one()
        if self.move_type not in ('out_invoice', 'out_refund'):
            return False

        self._check_period()

        return {
            'name': _('Create AR Adjustment'),
            'type': 'ir.actions.act_window',
            'res_model': 'ar.adjustment',
            'view_mode': 'form',
            'context': {
                'default_journal_id': self.journal_id.id,
                'default_partner_id': self.partner_id.id,
                'default_invoice_id': self.id,
                'default_state': 'draft',
                'default_from_invoice': True
            },
            'target': 'current',
        }

    def get_ar_adjustments(self):
        """
        Get all AR adjustments related to this invoice

        Returns:
            recordset: AR adjustment records
        """
        self.ensure_one()
        return self.adjustment_ids
