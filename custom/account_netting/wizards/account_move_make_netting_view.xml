<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <record id="act_account_move_make_netting" model="ir.actions.act_window">
            <field name="name">Compensate</field>
            <field name="res_model">account.move.make.netting</field>
            <field name="view_mode">form</field>
            <field name="target">new</field>
            <field name="binding_model_id" ref="account.model_account_move_line" />
            <field name="binding_view_types">list</field>
    </record>

    <record id="view_account_move_make_netting_form" model="ir.ui.view">
        <field name="name">Compensate entries</field>
        <field name="model">account.move.make.netting</field>
        <field name="arch" type="xml">
            <form>
                <p
                >This operation will generate a journal entry whose lines are counterpart of the receivable/payable accounts selected, and reconcile each other, letting this balance in the partner.</p>
                <group>
                    <field name="company_id" invisible="1" />
                    <field name="company_currency_id" invisible="1" />
                    <field name="partner_id" />
                    <field name="balance" />
                    <field name="balance_type" invisible="balance == 0" />
                    <field name="journal_id" />
					<field name="period_id" invisible="0"/>
                </group>
                <footer>
                    <button
                        name="button_compensate"
                        string="Compensate"
                        type="object"
                        default_focus="1"
                        class="btn-primary"
                    />
                    <button string="Cancel" class="btn-default" special="cancel" />
                </footer>
            </form>
        </field>
    </record>
</odoo>
