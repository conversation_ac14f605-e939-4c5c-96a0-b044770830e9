
# -*- coding: utf-8 -*-
###############################################################################
#    License, author and contributors information in:                         #
#    __manifest__.py file at the root folder of this module.                  #
###############################################################################

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError
from datetime import datetime
from dateutil.relativedelta import *
from contextlib import ExitStack, contextmanager


class WehaAccountMove(models.Model):
    _inherit = 'account.move'

        

    # Amount Fields
    # amount_untaxed = fields.Monetary(string='Untaxed Amount', compute='_compute_amount', store=True)
    dpp_nilai_lain = fields.Monetary(string='DPP Nilai Lain', compute='_compute_dpp_nilai_lain', store=True)
    # dpp_nilai_lain = fields.Monetary(string='DPP Nilai Lain', store=True)
    # amount_tax = fields.Monetary(string='Tax Amount', compute='_compute_amount', store=True)
    # amount_total = fields.Monetary(string='Total Amount', compute='_compute_amount', store=True)
    # currency_id = fields.Many2one('res.currency', string='Currency', default=lambda self: self.env.company.currency_id)
    


    # Compute
    @api.depends(
        'line_ids.matched_debit_ids.debit_move_id.move_id.origin_payment_id.is_matched',
        'line_ids.matched_debit_ids.debit_move_id.move_id.line_ids.amount_residual',
        'line_ids.matched_debit_ids.debit_move_id.move_id.line_ids.amount_residual_currency',
        'line_ids.matched_credit_ids.credit_move_id.move_id.origin_payment_id.is_matched',
        'line_ids.matched_credit_ids.credit_move_id.move_id.line_ids.amount_residual',
        'line_ids.matched_credit_ids.credit_move_id.move_id.line_ids.amount_residual_currency',
        'line_ids.balance',
        'line_ids.currency_id',
        'line_ids.amount_currency',
        'line_ids.amount_residual',
        'line_ids.amount_residual_currency',
        'line_ids.payment_id.state',
        'line_ids.full_reconcile_id',
        'state')
    def _compute_dpp_nilai_lain(self):
        res = super(WehaAccountMove, self)._compute_dpp_nilai_lain()
        for move in self:
            # amount_untaxed = 0
            # amount_tax = 0
            other_tax_base = 0
            # amount_total = 0
            if move.move_type == 'out_invoice':
                for line in move.invoice_line_ids:
                    # amount_untaxed += line.price_subtotal
                    # amount_tax += line.price_tax
                    other_tax_base += line.other_tax_base
                    # amount_total += line.price_total

                # move.amount_untaxed = amount_untaxed
                # move.amount_tax = amount_tax
                # move.amount_total = amount_total
                move.dpp_nilai_lain = other_tax_base
        return res
            
    # @contextmanager
    # def _check_balanced(self, container):
    #     ''' Assert the move is fully balanced debit = credit.
    #     An error is raised if it's not the case.
    #     '''
    #     with self._disable_recursion(container, 'check_move_validity', default=True, target=False) as disabled:
    #         yield
    #         if disabled:
    #             return

    

class WehaAccountMoveLine(models.Model):
    _inherit = 'account.move.line'



    # price_subtotal = fields.Monetary(string='Subtotal', compute='_compute_amount', store=True)
    # price_tax = fields.Monetary(string='Tax Amount', compute='_compute_amount', store=True)
    # price_total = fields.Monetary(string='Total', compute='_compute_amount', store=True)
    other_tax_base  = fields.Monetary(string='Other Tax Base', compute='_compute_dpp', store=True)
    # discount = fields.Float('Disc. (%)')
    amount_discount = fields.Monetary(string='Discount Amount', compute='_compute_discount', store=True)

        
    @api.depends('quantity', 'price_unit', 'tax_ids', 'amount_discount')
    def _compute_dpp(self):
        for line in self:
            price = (line.price_unit * line.quantity) - line.amount_discount
            # line.price_subtotal = price

            other_tax_base = 0
            check_taxes = line.tax_ids.filtered(lambda x: x.dpp_nilai_lain > 0)
            if check_taxes:
                other_tax_base = check_taxes[0].dpp_nilai_lain * price

            # taxes = line.tax_ids.compute_all(
            #     other_tax_base,
            #     line.currency_id,
            #     line.quantity,
            #     product=line.product_id,
            #     partner=line.move_id.partner_id
            # )            
            # line.price_tax = taxes['total_included'] - taxes['total_excluded']
            line.other_tax_base = other_tax_base

            # taxes = line.tax_ids.compute_all(
            #     price,
            #     line.currency_id,
            #     line.quantity,
            #     product=line.product_id,
            #     partner=line.move_id.partner_id
            # )
            # line.price_total = taxes['total_included']
    

    @api.depends('quantity', 'price_unit', 'discount')
    def _compute_discount(self):
        for line in self:
            line.amount_discount = line.price_unit * line.quantity * line.discount / 100