<odoo>
    <data>

    <record model="ir.ui.view" id="account_relocation_view_form">
        <field name="name">account.relocation.view.form</field>
        <field name="model">account.relocation</field>
        <field name="arch" type="xml">
            <form string="Budget Relocation">
                <header>
                    <button string="Approve" name="action_budget_approve" invisible="state != 'draft'" type="object"
                            class="oe_highlight"/>
                    <button string="Close" name="action_budget_done" invisible="state != 'validate'" type="object"
                            class="oe_highlight"/>
<!--                    <button string="Return" name="action_budget_return" invisible="state != 'approved'" type="object"-->
<!--                            class="oe_highlight"/>-->
                    <button string="Reset to Draft" name="action_budget_draft" invisible="state not in ['cancel', 'return']" type="object"/>
                    <button string="Cancel Budget" name="action_budget_cancel" invisible="state not in ('draft', 'pending_approval')" type="object"/>
                    <field name="state" widget="statusbar"/>
                </header>
                <sheet string="Budget Relocation">
                    <div class="oe_title">
                        <label for="name"/>
                        <h1>
                            <field name="name" readonly="1"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="date" readonly="state not in ['draft', 'return']"/>
                            <field name="employee_id" readonly="1" force_save="1"/>
                            <field name="requestor_id" readonly="state not in ['draft', 'return']"/>
                            <field name="unit_id" readonly="1" context="{'hierarchical_naming': False}" force_save="1"/>
                            <field name="group_id" readonly="1" context="{'hierarchical_naming': False}" force_save="1"/>
                            <field name="directorate_id" readonly="1" context="{'hierarchical_naming': False}" force_save="1"/>
<!--                            <field name="flag_lintas_tahun_active" readonly="state != 'draft'"/>-->
                            <field name="period_id" readonly="state != 'draft'"/>
<!--                            <field name="lt_period_id" readonly="state != 'draft'" invisible="not flag_lintas_tahun_active"/>-->
                        </group>
                        <group>
<!--                            <field name="budget_id" readonly="state != 'draft'" domain="[('active', '=', True), ('period_id', '=', period_id), ('state', '=', 'validate')]"/>-->
                            <field name="rkap_recipient_ids" widget="many2many_tags" invisible="1"/>
<!--                            <field name="rkap_recipient_id" readonly="state != 'draft'" domain="[('id', 'in', rkap_recipient_ids)]"/>-->
                            <field name="rkap_recipient_id" readonly="state != 'draft'"/>
                            <field name="company_currency_id" readonly="state != 'draft'"/>
                            <field name="rkap_budget_recipient_amount" readonly="1"/>
                            <field name="total_budget_amount" readonly="1"/>
                            <field name="ending_rkap_budget_recipient_amount" readonly="1"/>
                            <field name="attachment_id" widget="many2many_binary" string="Attach a file" readonly="state not in ['draft', 'return']"/>
                            <field name="general_budget_ids" widget="many2many_tags" invisible="1"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Budget Donor" name="relocation_lines">
                            <field name="line_ids" colspan="4"
                                   nolabel="1" readonly="state != 'draft'">
                                <list string="Budget Donor" editable="bottom">
                                    <field name="no"/>
                                    <field name="general_budget_id" domain="[('is_allocated', '=', True), ('id', 'in', parent.general_budget_ids)]"/>
<!--                                    <field name="unit_id" readonly="1" force_save="1"/>-->
                                    <field name="group_id" readonly="1" force_save="1"/>
                                    <field name="period_id" domain="['|', ('id', '=', parent.period_id), ('id', '=', parent.lt_period_id)]"/>
                                    <field name="company_currency_id"/>
                                    <field name="relocation_amount"/>
                                </list>
                                <form string="Budget Donor">
                                    <group>
                                        <group>
                                            <field name="description"/>
<!--                                            <field name="unit_id"/>-->
                                            <field name="group_id"/>
                                        </group>
                                        <group>
                                            <field name="period_id"/>
                                            <field name="relocation_amount"/>
                                        </group>
                                    </group>
                                </form>
                            </field>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

        <record id="view_account_relocation_tree" model="ir.ui.view">
            <field name="name">view.account.relocation.tree</field>
            <field name="model">account.relocation</field>
            <field name="arch" type="xml">
                <list>
                    <field name="name"/>
                    <field name="date"/>
                    <field name="employee_id"/>
                    <field name="requestor_id"/>
                    <field name="budget_id"/>
                    <field name="rkap_recipient_id"/>
                    <field name="rkap_budget_recipient_amount" column_invisible="1"/>
                    <field name="total_budget_amount" />
                    <field name="state"/>
                </list>
            </field>
        </record>

        <record id="action_account_relocation" model="ir.actions.act_window">
            <field name="name">Budget Relocation</field>
            <field name="res_model">account.relocation</field>
            <field name="view_mode">list,form</field>
        </record>

        <menuitem id="account_relocation_menu" action="action_account_relocation" parent="account_budget_control_ent.menu_budget" sequence="13"/>

        <record id="account_budget_control_ent.budget_allocation_menu" model="ir.ui.menu">
            <field name="active" eval="False"/>
        </record>

    </data>
</odoo>
